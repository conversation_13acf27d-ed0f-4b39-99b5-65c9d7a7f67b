"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvcnlhbndvbmcvZHlhZC1hcHBzL3ZpYmVjb2RlYmFzZS9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL3ByaW1pdGl2ZS9zcmMvcHJpbWl0aXZlLnRzeFxuZnVuY3Rpb24gY29tcG9zZUV2ZW50SGFuZGxlcnMob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlIH0gPSB7fSkge1xuICByZXR1cm4gZnVuY3Rpb24gaGFuZGxlRXZlbnQoZXZlbnQpIHtcbiAgICBvcmlnaW5hbEV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICBpZiAoY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID09PSBmYWxzZSB8fCAhZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgcmV0dXJuIG91ckV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBjb21wb3NlRXZlbnRIYW5kbGVyc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-arrow/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/arrow.tsx\n\n\n\nvar NAME = \"Arrow\";\nvar Arrow = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWFycm93L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDdUI7QUFDZDtBQUN4QztBQUNBLFlBQVksNkNBQWdCO0FBQzVCLFVBQVUsa0RBQWtEO0FBQzVELHlCQUF5QixzREFBRztBQUM1QixJQUFJLGdFQUFTO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsc0RBQUcsY0FBYywwQkFBMEI7QUFDdEc7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBSUU7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3J5YW53b25nL2R5YWQtYXBwcy92aWJlY29kZWJhc2Uvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1hcnJvdy9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvYXJyb3cudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IFByaW1pdGl2ZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlXCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBOQU1FID0gXCJBcnJvd1wiO1xudmFyIEFycm93ID0gUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICBjb25zdCB7IGNoaWxkcmVuLCB3aWR0aCA9IDEwLCBoZWlnaHQgPSA1LCAuLi5hcnJvd1Byb3BzIH0gPSBwcm9wcztcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgUHJpbWl0aXZlLnN2ZyxcbiAgICB7XG4gICAgICAuLi5hcnJvd1Byb3BzLFxuICAgICAgcmVmOiBmb3J3YXJkZWRSZWYsXG4gICAgICB3aWR0aCxcbiAgICAgIGhlaWdodCxcbiAgICAgIHZpZXdCb3g6IFwiMCAwIDMwIDEwXCIsXG4gICAgICBwcmVzZXJ2ZUFzcGVjdFJhdGlvOiBcIm5vbmVcIixcbiAgICAgIGNoaWxkcmVuOiBwcm9wcy5hc0NoaWxkID8gY2hpbGRyZW4gOiAvKiBAX19QVVJFX18gKi8ganN4KFwicG9seWdvblwiLCB7IHBvaW50czogXCIwLDAgMzAsMCAxNSwxMFwiIH0pXG4gICAgfVxuICApO1xufSk7XG5BcnJvdy5kaXNwbGF5TmFtZSA9IE5BTUU7XG52YXIgUm9vdCA9IEFycm93O1xuZXhwb3J0IHtcbiAgQXJyb3csXG4gIFJvb3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n            \"createCollection.CollectionItemSlot.useEffect\": ()=>{\n                context.itemMap.set(ref, {\n                    ref,\n                    ...itemData\n                });\n                return ({\n                    \"createCollection.CollectionItemSlot.useEffect\": ()=>void context.itemMap.delete(ref)\n                })[\"createCollection.CollectionItemSlot.useEffect\"];\n            }\n        }[\"createCollection.CollectionItemSlot.useEffect\"]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"createCollection.useCollection.useCallback[getItems]\": ()=>{\n                const collectionNode = context.collectionRef.current;\n                if (!collectionNode) return [];\n                const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n                const items = Array.from(context.itemMap.values());\n                const orderedItems = items.sort({\n                    \"createCollection.useCollection.useCallback[getItems].orderedItems\": (a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n                }[\"createCollection.useCollection.useCallback[getItems].orderedItems\"]);\n                return orderedItems;\n            }\n        }[\"createCollection.useCollection.useCallback[getItems]\"], [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n            \"createCollection2.CollectionProviderImpl.useEffect\": ()=>{\n                if (!collectionElement) return;\n                const observer = getChildListObserver({\n                    \"createCollection2.CollectionProviderImpl.useEffect.observer\": ()=>{}\n                }[\"createCollection2.CollectionProviderImpl.useEffect.observer\"]);\n                observer.observe(collectionElement, {\n                    childList: true,\n                    subtree: true\n                });\n                return ({\n                    \"createCollection2.CollectionProviderImpl.useEffect\": ()=>{\n                        observer.disconnect();\n                    }\n                })[\"createCollection2.CollectionProviderImpl.useEffect\"];\n            }\n        }[\"createCollection2.CollectionProviderImpl.useEffect\"], [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n            \"createCollection2.CollectionItemSlot.useEffect\": ()=>{\n                const itemData2 = memoizedItemData;\n                setItemMap({\n                    \"createCollection2.CollectionItemSlot.useEffect\": (map)=>{\n                        if (!element) {\n                            return map;\n                        }\n                        if (!map.has(element)) {\n                            map.set(element, {\n                                ...itemData2,\n                                element\n                            });\n                            return map.toSorted(sortByDocumentPosition);\n                        }\n                        return map.set(element, {\n                            ...itemData2,\n                            element\n                        }).toSorted(sortByDocumentPosition);\n                    }\n                }[\"createCollection2.CollectionItemSlot.useEffect\"]);\n                return ({\n                    \"createCollection2.CollectionItemSlot.useEffect\": ()=>{\n                        setItemMap({\n                            \"createCollection2.CollectionItemSlot.useEffect\": (map)=>{\n                                if (!element || !map.has(element)) {\n                                    return map;\n                                }\n                                map.delete(element);\n                                return new OrderedDict(map);\n                            }\n                        }[\"createCollection2.CollectionItemSlot.useEffect\"]);\n                    }\n                })[\"createCollection2.CollectionItemSlot.useEffect\"];\n            }\n        }[\"createCollection2.CollectionItemSlot.useEffect\"], [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQytCO0FBQ1M7QUFDeEM7QUFDQSxrQkFBa0IsZ0RBQW1CO0FBQ3JDO0FBQ0EsWUFBWSx1QkFBdUI7QUFDbkMsa0JBQWtCLDBDQUFhO0FBQy9CLDJCQUEyQixzREFBRyxxQkFBcUIsaUJBQWlCO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiw2Q0FBZ0I7QUFDcEM7QUFDQTtBQUNBLHlCQUF5QixhQUFhLDJCQUEyQixrQkFBa0I7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdEQUFtQjtBQUMzQztBQUNBO0FBQ0E7QUFDQSxjQUFjLDhCQUE4QjtBQUM1QztBQUNBLG9CQUFvQiwwQ0FBYTtBQUNqQyw2QkFBNkIsc0RBQUcscUJBQXFCLGlCQUFpQjtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2Q0FBZ0I7QUFDdEM7QUFDQTtBQUNBLDJCQUEyQixhQUFhLDJCQUEyQixrQkFBa0I7QUFDckY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsZ0RBQW1CO0FBQ2hDLEtBQUs7QUFDTDtBQUNBO0FBQ0EsYUFBYSwwQ0FBYTtBQUMxQixpQkFBaUIsV0FBVyxVQUFVLE1BQU0sbUNBQW1DO0FBQy9FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSwyREFBMkQscUJBQXFCO0FBQ2hGO0FBQ0Esa0RBQWtELFVBQVU7QUFDNUQsaUJBQWlCO0FBQ2pCLE9BQU8sSUFBSTtBQUNYLGFBQWEsMENBQWEsVUFBVSxXQUFXLG9CQUFvQixnQkFBZ0I7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yeWFud29uZy9keWFkLWFwcHMvdmliZWNvZGViYXNlL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtY29udGV4dC9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9jb250ZXh0L3NyYy9jcmVhdGUtY29udGV4dC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5mdW5jdGlvbiBjcmVhdGVDb250ZXh0Mihyb290Q29tcG9uZW50TmFtZSwgZGVmYXVsdENvbnRleHQpIHtcbiAgY29uc3QgQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoZGVmYXVsdENvbnRleHQpO1xuICBjb25zdCBQcm92aWRlciA9IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IHsgY2hpbGRyZW4sIC4uLmNvbnRleHQgfSA9IHByb3BzO1xuICAgIGNvbnN0IHZhbHVlID0gUmVhY3QudXNlTWVtbygoKSA9PiBjb250ZXh0LCBPYmplY3QudmFsdWVzKGNvbnRleHQpKTtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChDb250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlLCBjaGlsZHJlbiB9KTtcbiAgfTtcbiAgUHJvdmlkZXIuZGlzcGxheU5hbWUgPSByb290Q29tcG9uZW50TmFtZSArIFwiUHJvdmlkZXJcIjtcbiAgZnVuY3Rpb24gdXNlQ29udGV4dDIoY29uc3VtZXJOYW1lKSB7XG4gICAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoQ29udGV4dCk7XG4gICAgaWYgKGNvbnRleHQpIHJldHVybiBjb250ZXh0O1xuICAgIGlmIChkZWZhdWx0Q29udGV4dCAhPT0gdm9pZCAwKSByZXR1cm4gZGVmYXVsdENvbnRleHQ7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBcXGAke2NvbnN1bWVyTmFtZX1cXGAgbXVzdCBiZSB1c2VkIHdpdGhpbiBcXGAke3Jvb3RDb21wb25lbnROYW1lfVxcYGApO1xuICB9XG4gIHJldHVybiBbUHJvdmlkZXIsIHVzZUNvbnRleHQyXTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZUNvbnRleHRTY29wZShzY29wZU5hbWUsIGNyZWF0ZUNvbnRleHRTY29wZURlcHMgPSBbXSkge1xuICBsZXQgZGVmYXVsdENvbnRleHRzID0gW107XG4gIGZ1bmN0aW9uIGNyZWF0ZUNvbnRleHQzKHJvb3RDb21wb25lbnROYW1lLCBkZWZhdWx0Q29udGV4dCkge1xuICAgIGNvbnN0IEJhc2VDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dChkZWZhdWx0Q29udGV4dCk7XG4gICAgY29uc3QgaW5kZXggPSBkZWZhdWx0Q29udGV4dHMubGVuZ3RoO1xuICAgIGRlZmF1bHRDb250ZXh0cyA9IFsuLi5kZWZhdWx0Q29udGV4dHMsIGRlZmF1bHRDb250ZXh0XTtcbiAgICBjb25zdCBQcm92aWRlciA9IChwcm9wcykgPT4ge1xuICAgICAgY29uc3QgeyBzY29wZSwgY2hpbGRyZW4sIC4uLmNvbnRleHQgfSA9IHByb3BzO1xuICAgICAgY29uc3QgQ29udGV4dCA9IHNjb3BlPy5bc2NvcGVOYW1lXT8uW2luZGV4XSB8fCBCYXNlQ29udGV4dDtcbiAgICAgIGNvbnN0IHZhbHVlID0gUmVhY3QudXNlTWVtbygoKSA9PiBjb250ZXh0LCBPYmplY3QudmFsdWVzKGNvbnRleHQpKTtcbiAgICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KENvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWUsIGNoaWxkcmVuIH0pO1xuICAgIH07XG4gICAgUHJvdmlkZXIuZGlzcGxheU5hbWUgPSByb290Q29tcG9uZW50TmFtZSArIFwiUHJvdmlkZXJcIjtcbiAgICBmdW5jdGlvbiB1c2VDb250ZXh0Mihjb25zdW1lck5hbWUsIHNjb3BlKSB7XG4gICAgICBjb25zdCBDb250ZXh0ID0gc2NvcGU/LltzY29wZU5hbWVdPy5baW5kZXhdIHx8IEJhc2VDb250ZXh0O1xuICAgICAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoQ29udGV4dCk7XG4gICAgICBpZiAoY29udGV4dCkgcmV0dXJuIGNvbnRleHQ7XG4gICAgICBpZiAoZGVmYXVsdENvbnRleHQgIT09IHZvaWQgMCkgcmV0dXJuIGRlZmF1bHRDb250ZXh0O1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBcXGAke2NvbnN1bWVyTmFtZX1cXGAgbXVzdCBiZSB1c2VkIHdpdGhpbiBcXGAke3Jvb3RDb21wb25lbnROYW1lfVxcYGApO1xuICAgIH1cbiAgICByZXR1cm4gW1Byb3ZpZGVyLCB1c2VDb250ZXh0Ml07XG4gIH1cbiAgY29uc3QgY3JlYXRlU2NvcGUgPSAoKSA9PiB7XG4gICAgY29uc3Qgc2NvcGVDb250ZXh0cyA9IGRlZmF1bHRDb250ZXh0cy5tYXAoKGRlZmF1bHRDb250ZXh0KSA9PiB7XG4gICAgICByZXR1cm4gUmVhY3QuY3JlYXRlQ29udGV4dChkZWZhdWx0Q29udGV4dCk7XG4gICAgfSk7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIHVzZVNjb3BlKHNjb3BlKSB7XG4gICAgICBjb25zdCBjb250ZXh0cyA9IHNjb3BlPy5bc2NvcGVOYW1lXSB8fCBzY29wZUNvbnRleHRzO1xuICAgICAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oXG4gICAgICAgICgpID0+ICh7IFtgX19zY29wZSR7c2NvcGVOYW1lfWBdOiB7IC4uLnNjb3BlLCBbc2NvcGVOYW1lXTogY29udGV4dHMgfSB9KSxcbiAgICAgICAgW3Njb3BlLCBjb250ZXh0c11cbiAgICAgICk7XG4gICAgfTtcbiAgfTtcbiAgY3JlYXRlU2NvcGUuc2NvcGVOYW1lID0gc2NvcGVOYW1lO1xuICByZXR1cm4gW2NyZWF0ZUNvbnRleHQzLCBjb21wb3NlQ29udGV4dFNjb3BlcyhjcmVhdGVTY29wZSwgLi4uY3JlYXRlQ29udGV4dFNjb3BlRGVwcyldO1xufVxuZnVuY3Rpb24gY29tcG9zZUNvbnRleHRTY29wZXMoLi4uc2NvcGVzKSB7XG4gIGNvbnN0IGJhc2VTY29wZSA9IHNjb3Blc1swXTtcbiAgaWYgKHNjb3Blcy5sZW5ndGggPT09IDEpIHJldHVybiBiYXNlU2NvcGU7XG4gIGNvbnN0IGNyZWF0ZVNjb3BlID0gKCkgPT4ge1xuICAgIGNvbnN0IHNjb3BlSG9va3MgPSBzY29wZXMubWFwKChjcmVhdGVTY29wZTIpID0+ICh7XG4gICAgICB1c2VTY29wZTogY3JlYXRlU2NvcGUyKCksXG4gICAgICBzY29wZU5hbWU6IGNyZWF0ZVNjb3BlMi5zY29wZU5hbWVcbiAgICB9KSk7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIHVzZUNvbXBvc2VkU2NvcGVzKG92ZXJyaWRlU2NvcGVzKSB7XG4gICAgICBjb25zdCBuZXh0U2NvcGVzID0gc2NvcGVIb29rcy5yZWR1Y2UoKG5leHRTY29wZXMyLCB7IHVzZVNjb3BlLCBzY29wZU5hbWUgfSkgPT4ge1xuICAgICAgICBjb25zdCBzY29wZVByb3BzID0gdXNlU2NvcGUob3ZlcnJpZGVTY29wZXMpO1xuICAgICAgICBjb25zdCBjdXJyZW50U2NvcGUgPSBzY29wZVByb3BzW2BfX3Njb3BlJHtzY29wZU5hbWV9YF07XG4gICAgICAgIHJldHVybiB7IC4uLm5leHRTY29wZXMyLCAuLi5jdXJyZW50U2NvcGUgfTtcbiAgICAgIH0sIHt9KTtcbiAgICAgIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICh7IFtgX19zY29wZSR7YmFzZVNjb3BlLnNjb3BlTmFtZX1gXTogbmV4dFNjb3BlcyB9KSwgW25leHRTY29wZXNdKTtcbiAgICB9O1xuICB9O1xuICBjcmVhdGVTY29wZS5zY29wZU5hbWUgPSBiYXNlU2NvcGUuc2NvcGVOYW1lO1xuICByZXR1cm4gY3JlYXRlU2NvcGU7XG59XG5leHBvcnQge1xuICBjcmVhdGVDb250ZXh0MiBhcyBjcmVhdGVDb250ZXh0LFxuICBjcmVhdGVDb250ZXh0U2NvcGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, {\n        \"DismissableLayer.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"DismissableLayer.useComposedRefs[composedRefs]\"]);\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside({\n        \"DismissableLayer.usePointerDownOutside[pointerDownOutside]\": (event)=>{\n            const target = event.target;\n            const isPointerDownOnBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\"]);\n            if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n            onPointerDownOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside]\"], ownerDocument);\n    const focusOutside = useFocusOutside({\n        \"DismissableLayer.useFocusOutside[focusOutside]\": (event)=>{\n            const target = event.target;\n            const isFocusInBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\"]);\n            if (isFocusInBranch) return;\n            onFocusOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.useFocusOutside[focusOutside]\"], ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)({\n        \"DismissableLayer.useEscapeKeydown\": (event)=>{\n            const isHighestLayer = index === context.layers.size - 1;\n            if (!isHighestLayer) return;\n            onEscapeKeyDown?.(event);\n            if (!event.defaultPrevented && onDismiss) {\n                event.preventDefault();\n                onDismiss();\n            }\n        }\n    }[\"DismissableLayer.useEscapeKeydown\"], ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            if (!node) return;\n            if (disableOutsidePointerEvents) {\n                if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                    originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                    ownerDocument.body.style.pointerEvents = \"none\";\n                }\n                context.layersWithOutsidePointerEventsDisabled.add(node);\n            }\n            context.layers.add(node);\n            dispatchUpdate();\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                        ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n                    }\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (!node) return;\n                    context.layers.delete(node);\n                    context.layersWithOutsidePointerEventsDisabled.delete(node);\n                    dispatchUpdate();\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            const handleUpdate = {\n                \"DismissableLayer.useEffect.handleUpdate\": ()=>force({})\n            }[\"DismissableLayer.useEffect.handleUpdate\"];\n            document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n            return ({\n                \"DismissableLayer.useEffect\": ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate)\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayerBranch.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                context.branches.add(node);\n                return ({\n                    \"DismissableLayerBranch.useEffect\": ()=>{\n                        context.branches.delete(node);\n                    }\n                })[\"DismissableLayerBranch.useEffect\"];\n            }\n        }\n    }[\"DismissableLayerBranch.useEffect\"], [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        \"usePointerDownOutside.useRef[handleClickRef]\": ()=>{}\n    }[\"usePointerDownOutside.useRef[handleClickRef]\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePointerDownOutside.useEffect\": ()=>{\n            const handlePointerDown = {\n                \"usePointerDownOutside.useEffect.handlePointerDown\": (event)=>{\n                    if (event.target && !isPointerInsideReactTreeRef.current) {\n                        let handleAndDispatchPointerDownOutsideEvent2 = {\n                            \"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\": function() {\n                                handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                                    discrete: true\n                                });\n                            }\n                        }[\"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\"];\n                        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        if (event.pointerType === \"touch\") {\n                            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                            handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                            ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                                once: true\n                            });\n                        } else {\n                            handleAndDispatchPointerDownOutsideEvent2();\n                        }\n                    } else {\n                        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    }\n                    isPointerInsideReactTreeRef.current = false;\n                }\n            }[\"usePointerDownOutside.useEffect.handlePointerDown\"];\n            const timerId = window.setTimeout({\n                \"usePointerDownOutside.useEffect.timerId\": ()=>{\n                    ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n                }\n            }[\"usePointerDownOutside.useEffect.timerId\"], 0);\n            return ({\n                \"usePointerDownOutside.useEffect\": ()=>{\n                    window.clearTimeout(timerId);\n                    ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                }\n            })[\"usePointerDownOutside.useEffect\"];\n        }\n    }[\"usePointerDownOutside.useEffect\"], [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusOutside.useEffect\": ()=>{\n            const handleFocus = {\n                \"useFocusOutside.useEffect.handleFocus\": (event)=>{\n                    if (event.target && !isFocusInsideReactTreeRef.current) {\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                            discrete: false\n                        });\n                    }\n                }\n            }[\"useFocusOutside.useEffect.handleFocus\"];\n            ownerDocument.addEventListener(\"focusin\", handleFocus);\n            return ({\n                \"useFocusOutside.useEffect\": ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus)\n            })[\"useFocusOutside.useEffect\"];\n        }\n    }[\"useFocusOutside.useEffect\"], [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpc21pc3NhYmxlLWxheWVyL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUF1QjtBQUNjO0FBQ2tCO0FBQ3ZCO0FBQ0Q7QUFDRTtBQXFKM0I7QUEvSU4sSUFBTSx5QkFBeUI7QUFDL0IsSUFBTSxpQkFBaUI7QUFDdkIsSUFBTSx1QkFBdUI7QUFDN0IsSUFBTSxnQkFBZ0I7QUFFdEIsSUFBSTtBQUVKLElBQU0sd0NBQWdDLGlEQUFjO0lBQ2xELFFBQVEsb0JBQUksSUFBNkI7SUFDekMsd0NBQXdDLG9CQUFJLElBQTZCO0lBQ3pFLFVBQVUsb0JBQUksSUFBbUM7QUFDbkQsQ0FBQztBQXNDRCxJQUFNLGlDQUF5Qiw4Q0FDN0IsQ0FBQyxPQUFPO0lBQ04sTUFBTSxFQUNKLDhCQUE4QixPQUM5QixpQkFDQSxzQkFDQSxnQkFDQSxtQkFDQSxXQUNBLEdBQUcsWUFDTCxHQUFJO0lBQ0osTUFBTSxVQUFnQiw4Q0FBVyx1QkFBdUI7SUFDeEQsTUFBTSxDQUFDLE1BQU0sT0FBTyxJQUFVLDRDQUF5QyxJQUFJO0lBQzNFLE1BQU0sZ0JBQWdCLE1BQU0saUJBQWlCLFlBQVk7SUFDekQsTUFBTSxDQUFDLEVBQUUsS0FBSyxJQUFVLDRDQUFTLENBQUMsQ0FBQztJQUNuQyxNQUFNLGVBQWUsNkVBQWUsQ0FBQzswREFBYyxDQUFDQSxRQUFTLFFBQVFBLEtBQUksQ0FBQzs7SUFDMUUsTUFBTSxTQUFTLE1BQU0sS0FBSyxRQUFRLE1BQU07SUFDeEMsTUFBTSxDQUFDLDRDQUE0QyxJQUFJLENBQUM7V0FBRyxRQUFRLHNDQUFzQztLQUFBLENBQUUsTUFBTSxFQUFFO0lBQ25ILE1BQU0sb0RBQW9ELE9BQU8sUUFBUSw0Q0FBNkM7SUFDdEgsTUFBTSxRQUFRLE9BQU8sT0FBTyxRQUFRLElBQUksSUFBSTtJQUM1QyxNQUFNLDhCQUE4QixRQUFRLHVDQUF1QyxPQUFPO0lBQzFGLE1BQU0seUJBQXlCLFNBQVM7SUFFeEMsTUFBTSxxQkFBcUI7c0VBQXNCLENBQUM7WUFDaEQsTUFBTSxTQUFTLE1BQU07WUFDckIsTUFBTSx3QkFBd0IsQ0FBQzttQkFBRyxRQUFRLFFBQVE7YUFBQSxDQUFFO29HQUFLLENBQUMsU0FBVyxPQUFPLFNBQVMsTUFBTSxDQUFDOztZQUM1RixJQUFJLENBQUMsMEJBQTBCLHNCQUF1QjtZQUN0RCx1QkFBdUIsS0FBSztZQUM1QixvQkFBb0IsS0FBSztZQUN6QixJQUFJLENBQUMsTUFBTSxpQkFBa0IsYUFBWTtRQUMzQztxRUFBRyxhQUFhO0lBRWhCLE1BQU0sZUFBZTswREFBZ0IsQ0FBQztZQUNwQyxNQUFNLFNBQVMsTUFBTTtZQUNyQixNQUFNLGtCQUFrQixDQUFDO21CQUFHLFFBQVEsUUFBUTthQUFBLENBQUU7a0ZBQUssQ0FBQyxTQUFXLE9BQU8sU0FBUyxNQUFNLENBQUM7O1lBQ3RGLElBQUksZ0JBQWlCO1lBQ3JCLGlCQUFpQixLQUFLO1lBQ3RCLG9CQUFvQixLQUFLO1lBQ3pCLElBQUksQ0FBQyxNQUFNLGlCQUFrQixhQUFZO1FBQzNDO3lEQUFHLGFBQWE7SUFFaEIsb0ZBQWdCOzZDQUFDLENBQUM7WUFDaEIsTUFBTSxpQkFBaUIsVUFBVSxRQUFRLE9BQU8sT0FBTztZQUN2RCxJQUFJLENBQUMsZUFBZ0I7WUFDckIsa0JBQWtCLEtBQUs7WUFDdkIsSUFBSSxDQUFDLE1BQU0sb0JBQW9CLFdBQVc7Z0JBQ3hDLE1BQU0sZUFBZTtnQkFDckIsVUFBVTtZQUNaO1FBQ0Y7NENBQUcsYUFBYTtJQUVWO3NDQUFVO1lBQ2QsSUFBSSxDQUFDLEtBQU07WUFDWCxJQUFJLDZCQUE2QjtnQkFDL0IsSUFBSSxRQUFRLHVDQUF1QyxTQUFTLEdBQUc7b0JBQzdELDRCQUE0QixjQUFjLEtBQUssTUFBTTtvQkFDckQsY0FBYyxLQUFLLE1BQU0sZ0JBQWdCO2dCQUMzQztnQkFDQSxRQUFRLHVDQUF1QyxJQUFJLElBQUk7WUFDekQ7WUFDQSxRQUFRLE9BQU8sSUFBSSxJQUFJO1lBQ3ZCLGVBQWU7WUFDZjs4Q0FBTztvQkFDTCxJQUNFLCtCQUNBLFFBQVEsdUNBQXVDLFNBQVMsR0FDeEQ7d0JBQ0EsY0FBYyxLQUFLLE1BQU0sZ0JBQWdCO29CQUMzQztnQkFDRjs7UUFDRjtxQ0FBRztRQUFDO1FBQU07UUFBZTtRQUE2QixPQUFPO0tBQUM7SUFReEQ7c0NBQVU7WUFDZDs4Q0FBTztvQkFDTCxJQUFJLENBQUMsS0FBTTtvQkFDWCxRQUFRLE9BQU8sT0FBTyxJQUFJO29CQUMxQixRQUFRLHVDQUF1QyxPQUFPLElBQUk7b0JBQzFELGVBQWU7Z0JBQ2pCOztRQUNGO3FDQUFHO1FBQUM7UUFBTSxPQUFPO0tBQUM7SUFFWjtzQ0FBVTtZQUNkLE1BQU07MkRBQWUsSUFBTSxNQUFNLENBQUMsQ0FBQzs7WUFDbkMsU0FBUyxpQkFBaUIsZ0JBQWdCLFlBQVk7WUFDdEQ7OENBQU8sSUFBTSxTQUFTLG9CQUFvQixnQkFBZ0IsWUFBWTs7UUFDeEU7cUNBQUcsQ0FBQyxDQUFDO0lBRUwsT0FDRSx1RUFBQyxnRUFBUyxDQUFDLEtBQVY7UUFDRSxHQUFHO1FBQ0osS0FBSztRQUNMLE9BQU87WUFDTCxlQUFlLDhCQUNYLHlCQUNFLFNBQ0EsU0FDRjtZQUNKLEdBQUcsTUFBTTtRQUNYO1FBQ0EsZ0JBQWdCLHlFQUFvQixDQUFDLE1BQU0sZ0JBQWdCLGFBQWEsY0FBYztRQUN0RixlQUFlLHlFQUFvQixDQUFDLE1BQU0sZUFBZSxhQUFhLGFBQWE7UUFDbkYsc0JBQXNCLHlFQUFvQixDQUN4QyxNQUFNLHNCQUNOLG1CQUFtQjtJQUNyQjtBQUdOO0FBR0YsaUJBQWlCLGNBQWM7QUFNL0IsSUFBTSxjQUFjO0FBS3BCLElBQU0sdUNBQStCLDhDQUduQyxDQUFDLE9BQU87SUFDUixNQUFNLFVBQWdCLDhDQUFXLHVCQUF1QjtJQUN4RCxNQUFNLE1BQVksMENBQXNDLElBQUk7SUFDNUQsTUFBTSxlQUFlLDZFQUFlLENBQUMsY0FBYyxHQUFHO0lBRWhEOzRDQUFVO1lBQ2QsTUFBTSxPQUFPLElBQUk7WUFDakIsSUFBSSxNQUFNO2dCQUNSLFFBQVEsU0FBUyxJQUFJLElBQUk7Z0JBQ3pCO3dEQUFPO3dCQUNMLFFBQVEsU0FBUyxPQUFPLElBQUk7b0JBQzlCOztZQUNGO1FBQ0Y7MkNBQUc7UUFBQyxRQUFRLFFBQVE7S0FBQztJQUVyQixPQUFPLHVFQUFDLGdFQUFTLENBQUMsS0FBVjtRQUFlLEdBQUc7UUFBTyxLQUFLO0lBQUEsQ0FBYztBQUN0RCxDQUFDO0FBRUQsdUJBQXVCLGNBQWM7QUFZckMsU0FBUyxzQkFDUCxzQkFDQSxnQkFBMEIsWUFBWSxVQUN0QztJQUNBLE1BQU0sMkJBQTJCLGdGQUFjLENBQUMsb0JBQW9CO0lBQ3BFLE1BQU0sOEJBQW9DLDBDQUFPLEtBQUs7SUFDdEQsTUFBTSxpQkFBdUI7d0RBQU8sS0FBTyxDQUFEOztJQUVwQzsyQ0FBVTtZQUNkLE1BQU07cUVBQW9CLENBQUM7b0JBQ3pCLElBQUksTUFBTSxVQUFVLENBQUMsNEJBQTRCLFNBQVM7d0JBR3hELElBQVNDOzJIQUFULFdBQW9EO2dDQUNsRCw2QkFDRSxzQkFDQSwwQkFDQSxhQUNBO29DQUFFLFVBQVU7Z0NBQUs7NEJBRXJCOzt3QkFQUywrQ0FBQUE7d0JBRlQsTUFBTSxjQUFjOzRCQUFFLGVBQWU7d0JBQU07d0JBdUIzQyxJQUFJLE1BQU0sZ0JBQWdCLFNBQVM7NEJBQ2pDLGNBQWMsb0JBQW9CLFNBQVMsZUFBZSxPQUFPOzRCQUNqRSxlQUFlLFVBQVVBOzRCQUN6QixjQUFjLGlCQUFpQixTQUFTLGVBQWUsU0FBUztnQ0FBRSxNQUFNOzRCQUFLLENBQUM7d0JBQ2hGLE9BQU87NEJBQ0xBLDBDQUF5Qzt3QkFDM0M7b0JBQ0YsT0FBTzt3QkFHTCxjQUFjLG9CQUFvQixTQUFTLGVBQWUsT0FBTztvQkFDbkU7b0JBQ0EsNEJBQTRCLFVBQVU7Z0JBQ3hDOztZQWNBLE1BQU0sVUFBVSxPQUFPOzJEQUFXO29CQUNoQyxjQUFjLGlCQUFpQixlQUFlLGlCQUFpQjtnQkFDakU7MERBQUcsQ0FBQztZQUNKO21EQUFPO29CQUNMLE9BQU8sYUFBYSxPQUFPO29CQUMzQixjQUFjLG9CQUFvQixlQUFlLGlCQUFpQjtvQkFDbEUsY0FBYyxvQkFBb0IsU0FBUyxlQUFlLE9BQU87Z0JBQ25FOztRQUNGOzBDQUFHO1FBQUM7UUFBZSx3QkFBd0I7S0FBQztJQUU1QyxPQUFPO1FBQUE7UUFFTCxzQkFBc0IsSUFBTyw0QkFBNEIsVUFBVTtJQUNyRTtBQUNGO0FBTUEsU0FBUyxnQkFDUCxnQkFDQSxnQkFBMEIsWUFBWSxVQUN0QztJQUNBLE1BQU0scUJBQXFCLGdGQUFjLENBQUMsY0FBYztJQUN4RCxNQUFNLDRCQUFrQywwQ0FBTyxLQUFLO0lBRTlDO3FDQUFVO1lBQ2QsTUFBTTt5REFBYyxDQUFDO29CQUNuQixJQUFJLE1BQU0sVUFBVSxDQUFDLDBCQUEwQixTQUFTO3dCQUN0RCxNQUFNLGNBQWM7NEJBQUUsZUFBZTt3QkFBTTt3QkFDM0MsNkJBQTZCLGVBQWUsb0JBQW9CLGFBQWE7NEJBQzNFLFVBQVU7d0JBQ1osQ0FBQztvQkFDSDtnQkFDRjs7WUFDQSxjQUFjLGlCQUFpQixXQUFXLFdBQVc7WUFDckQ7NkNBQU8sSUFBTSxjQUFjLG9CQUFvQixXQUFXLFdBQVc7O1FBQ3ZFO29DQUFHO1FBQUM7UUFBZSxrQkFBa0I7S0FBQztJQUV0QyxPQUFPO1FBQ0wsZ0JBQWdCLElBQU8sMEJBQTBCLFVBQVU7UUFDM0QsZUFBZSxJQUFPLDBCQUEwQixVQUFVO0lBQzVEO0FBQ0Y7QUFFQSxTQUFTLGlCQUFpQjtJQUN4QixNQUFNLFFBQVEsSUFBSSxZQUFZLGNBQWM7SUFDNUMsU0FBUyxjQUFjLEtBQUs7QUFDOUI7QUFFQSxTQUFTLDZCQUNQLE1BQ0EsU0FDQSxRQUNBLEVBQUUsU0FBUyxHQUNYO0lBQ0EsTUFBTSxTQUFTLE9BQU8sY0FBYztJQUNwQyxNQUFNLFFBQVEsSUFBSSxZQUFZLE1BQU07UUFBRSxTQUFTO1FBQU8sWUFBWTtRQUFNO0lBQU8sQ0FBQztJQUNoRixJQUFJLFFBQVMsUUFBTyxpQkFBaUIsTUFBTSxTQUEwQjtRQUFFLE1BQU07SUFBSyxDQUFDO0lBRW5GLElBQUksVUFBVTtRQUNaLHNGQUEyQixDQUFDLFFBQVEsS0FBSztJQUMzQyxPQUFPO1FBQ0wsT0FBTyxjQUFjLEtBQUs7SUFDNUI7QUFDRjtBQUVBLElBQU0sT0FBTztBQUNiLElBQU0sU0FBUyIsInNvdXJjZXMiOlsiL1VzZXJzL3J5YW53b25nL2R5YWQtYXBwcy9zcmMvZGlzbWlzc2FibGUtbGF5ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNvbXBvc2VFdmVudEhhbmRsZXJzIH0gZnJvbSAnQHJhZGl4LXVpL3ByaW1pdGl2ZSc7XG5pbXBvcnQgeyBQcmltaXRpdmUsIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudCB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1wcmltaXRpdmUnO1xuaW1wb3J0IHsgdXNlQ29tcG9zZWRSZWZzIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcyc7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmJztcbmltcG9ydCB7IHVzZUVzY2FwZUtleWRvd24gfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWVzY2FwZS1rZXlkb3duJztcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogRGlzbWlzc2FibGVMYXllclxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBESVNNSVNTQUJMRV9MQVlFUl9OQU1FID0gJ0Rpc21pc3NhYmxlTGF5ZXInO1xuY29uc3QgQ09OVEVYVF9VUERBVEUgPSAnZGlzbWlzc2FibGVMYXllci51cGRhdGUnO1xuY29uc3QgUE9JTlRFUl9ET1dOX09VVFNJREUgPSAnZGlzbWlzc2FibGVMYXllci5wb2ludGVyRG93bk91dHNpZGUnO1xuY29uc3QgRk9DVVNfT1VUU0lERSA9ICdkaXNtaXNzYWJsZUxheWVyLmZvY3VzT3V0c2lkZSc7XG5cbmxldCBvcmlnaW5hbEJvZHlQb2ludGVyRXZlbnRzOiBzdHJpbmc7XG5cbmNvbnN0IERpc21pc3NhYmxlTGF5ZXJDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh7XG4gIGxheWVyczogbmV3IFNldDxEaXNtaXNzYWJsZUxheWVyRWxlbWVudD4oKSxcbiAgbGF5ZXJzV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWQ6IG5ldyBTZXQ8RGlzbWlzc2FibGVMYXllckVsZW1lbnQ+KCksXG4gIGJyYW5jaGVzOiBuZXcgU2V0PERpc21pc3NhYmxlTGF5ZXJCcmFuY2hFbGVtZW50PigpLFxufSk7XG5cbnR5cGUgRGlzbWlzc2FibGVMYXllckVsZW1lbnQgPSBSZWFjdC5Db21wb25lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xudHlwZSBQcmltaXRpdmVEaXZQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLmRpdj47XG5pbnRlcmZhY2UgRGlzbWlzc2FibGVMYXllclByb3BzIGV4dGVuZHMgUHJpbWl0aXZlRGl2UHJvcHMge1xuICAvKipcbiAgICogV2hlbiBgdHJ1ZWAsIGhvdmVyL2ZvY3VzL2NsaWNrIGludGVyYWN0aW9ucyB3aWxsIGJlIGRpc2FibGVkIG9uIGVsZW1lbnRzIG91dHNpZGVcbiAgICogdGhlIGBEaXNtaXNzYWJsZUxheWVyYC4gVXNlcnMgd2lsbCBuZWVkIHRvIGNsaWNrIHR3aWNlIG9uIG91dHNpZGUgZWxlbWVudHMgdG9cbiAgICogaW50ZXJhY3Qgd2l0aCB0aGVtOiBvbmNlIHRvIGNsb3NlIHRoZSBgRGlzbWlzc2FibGVMYXllcmAsIGFuZCBhZ2FpbiB0byB0cmlnZ2VyIHRoZSBlbGVtZW50LlxuICAgKi9cbiAgZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzPzogYm9vbGVhbjtcbiAgLyoqXG4gICAqIEV2ZW50IGhhbmRsZXIgY2FsbGVkIHdoZW4gdGhlIGVzY2FwZSBrZXkgaXMgZG93bi5cbiAgICogQ2FuIGJlIHByZXZlbnRlZC5cbiAgICovXG4gIG9uRXNjYXBlS2V5RG93bj86IChldmVudDogS2V5Ym9hcmRFdmVudCkgPT4gdm9pZDtcbiAgLyoqXG4gICAqIEV2ZW50IGhhbmRsZXIgY2FsbGVkIHdoZW4gdGhlIGEgYHBvaW50ZXJkb3duYCBldmVudCBoYXBwZW5zIG91dHNpZGUgb2YgdGhlIGBEaXNtaXNzYWJsZUxheWVyYC5cbiAgICogQ2FuIGJlIHByZXZlbnRlZC5cbiAgICovXG4gIG9uUG9pbnRlckRvd25PdXRzaWRlPzogKGV2ZW50OiBQb2ludGVyRG93bk91dHNpZGVFdmVudCkgPT4gdm9pZDtcbiAgLyoqXG4gICAqIEV2ZW50IGhhbmRsZXIgY2FsbGVkIHdoZW4gdGhlIGZvY3VzIG1vdmVzIG91dHNpZGUgb2YgdGhlIGBEaXNtaXNzYWJsZUxheWVyYC5cbiAgICogQ2FuIGJlIHByZXZlbnRlZC5cbiAgICovXG4gIG9uRm9jdXNPdXRzaWRlPzogKGV2ZW50OiBGb2N1c091dHNpZGVFdmVudCkgPT4gdm9pZDtcbiAgLyoqXG4gICAqIEV2ZW50IGhhbmRsZXIgY2FsbGVkIHdoZW4gYW4gaW50ZXJhY3Rpb24gaGFwcGVucyBvdXRzaWRlIHRoZSBgRGlzbWlzc2FibGVMYXllcmAuXG4gICAqIFNwZWNpZmljYWxseSwgd2hlbiBhIGBwb2ludGVyZG93bmAgZXZlbnQgaGFwcGVucyBvdXRzaWRlIG9yIGZvY3VzIG1vdmVzIG91dHNpZGUgb2YgaXQuXG4gICAqIENhbiBiZSBwcmV2ZW50ZWQuXG4gICAqL1xuICBvbkludGVyYWN0T3V0c2lkZT86IChldmVudDogUG9pbnRlckRvd25PdXRzaWRlRXZlbnQgfCBGb2N1c091dHNpZGVFdmVudCkgPT4gdm9pZDtcbiAgLyoqXG4gICAqIEhhbmRsZXIgY2FsbGVkIHdoZW4gdGhlIGBEaXNtaXNzYWJsZUxheWVyYCBzaG91bGQgYmUgZGlzbWlzc2VkXG4gICAqL1xuICBvbkRpc21pc3M/OiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBEaXNtaXNzYWJsZUxheWVyID0gUmVhY3QuZm9yd2FyZFJlZjxEaXNtaXNzYWJsZUxheWVyRWxlbWVudCwgRGlzbWlzc2FibGVMYXllclByb3BzPihcbiAgKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7XG4gICAgICBkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHMgPSBmYWxzZSxcbiAgICAgIG9uRXNjYXBlS2V5RG93bixcbiAgICAgIG9uUG9pbnRlckRvd25PdXRzaWRlLFxuICAgICAgb25Gb2N1c091dHNpZGUsXG4gICAgICBvbkludGVyYWN0T3V0c2lkZSxcbiAgICAgIG9uRGlzbWlzcyxcbiAgICAgIC4uLmxheWVyUHJvcHNcbiAgICB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoRGlzbWlzc2FibGVMYXllckNvbnRleHQpO1xuICAgIGNvbnN0IFtub2RlLCBzZXROb2RlXSA9IFJlYWN0LnVzZVN0YXRlPERpc21pc3NhYmxlTGF5ZXJFbGVtZW50IHwgbnVsbD4obnVsbCk7XG4gICAgY29uc3Qgb3duZXJEb2N1bWVudCA9IG5vZGU/Lm93bmVyRG9jdW1lbnQgPz8gZ2xvYmFsVGhpcz8uZG9jdW1lbnQ7XG4gICAgY29uc3QgWywgZm9yY2VdID0gUmVhY3QudXNlU3RhdGUoe30pO1xuICAgIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIChub2RlKSA9PiBzZXROb2RlKG5vZGUpKTtcbiAgICBjb25zdCBsYXllcnMgPSBBcnJheS5mcm9tKGNvbnRleHQubGF5ZXJzKTtcbiAgICBjb25zdCBbaGlnaGVzdExheWVyV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWRdID0gWy4uLmNvbnRleHQubGF5ZXJzV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWRdLnNsaWNlKC0xKTsgLy8gcHJldHRpZXItaWdub3JlXG4gICAgY29uc3QgaGlnaGVzdExheWVyV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWRJbmRleCA9IGxheWVycy5pbmRleE9mKGhpZ2hlc3RMYXllcldpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkISk7IC8vIHByZXR0aWVyLWlnbm9yZVxuICAgIGNvbnN0IGluZGV4ID0gbm9kZSA/IGxheWVycy5pbmRleE9mKG5vZGUpIDogLTE7XG4gICAgY29uc3QgaXNCb2R5UG9pbnRlckV2ZW50c0Rpc2FibGVkID0gY29udGV4dC5sYXllcnNXaXRoT3V0c2lkZVBvaW50ZXJFdmVudHNEaXNhYmxlZC5zaXplID4gMDtcbiAgICBjb25zdCBpc1BvaW50ZXJFdmVudHNFbmFibGVkID0gaW5kZXggPj0gaGlnaGVzdExheWVyV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWRJbmRleDtcblxuICAgIGNvbnN0IHBvaW50ZXJEb3duT3V0c2lkZSA9IHVzZVBvaW50ZXJEb3duT3V0c2lkZSgoZXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldCBhcyBIVE1MRWxlbWVudDtcbiAgICAgIGNvbnN0IGlzUG9pbnRlckRvd25PbkJyYW5jaCA9IFsuLi5jb250ZXh0LmJyYW5jaGVzXS5zb21lKChicmFuY2gpID0+IGJyYW5jaC5jb250YWlucyh0YXJnZXQpKTtcbiAgICAgIGlmICghaXNQb2ludGVyRXZlbnRzRW5hYmxlZCB8fCBpc1BvaW50ZXJEb3duT25CcmFuY2gpIHJldHVybjtcbiAgICAgIG9uUG9pbnRlckRvd25PdXRzaWRlPy4oZXZlbnQpO1xuICAgICAgb25JbnRlcmFjdE91dHNpZGU/LihldmVudCk7XG4gICAgICBpZiAoIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIG9uRGlzbWlzcz8uKCk7XG4gICAgfSwgb3duZXJEb2N1bWVudCk7XG5cbiAgICBjb25zdCBmb2N1c091dHNpZGUgPSB1c2VGb2N1c091dHNpZGUoKGV2ZW50KSA9PiB7XG4gICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQgYXMgSFRNTEVsZW1lbnQ7XG4gICAgICBjb25zdCBpc0ZvY3VzSW5CcmFuY2ggPSBbLi4uY29udGV4dC5icmFuY2hlc10uc29tZSgoYnJhbmNoKSA9PiBicmFuY2guY29udGFpbnModGFyZ2V0KSk7XG4gICAgICBpZiAoaXNGb2N1c0luQnJhbmNoKSByZXR1cm47XG4gICAgICBvbkZvY3VzT3V0c2lkZT8uKGV2ZW50KTtcbiAgICAgIG9uSW50ZXJhY3RPdXRzaWRlPy4oZXZlbnQpO1xuICAgICAgaWYgKCFldmVudC5kZWZhdWx0UHJldmVudGVkKSBvbkRpc21pc3M/LigpO1xuICAgIH0sIG93bmVyRG9jdW1lbnQpO1xuXG4gICAgdXNlRXNjYXBlS2V5ZG93bigoZXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IGlzSGlnaGVzdExheWVyID0gaW5kZXggPT09IGNvbnRleHQubGF5ZXJzLnNpemUgLSAxO1xuICAgICAgaWYgKCFpc0hpZ2hlc3RMYXllcikgcmV0dXJuO1xuICAgICAgb25Fc2NhcGVLZXlEb3duPy4oZXZlbnQpO1xuICAgICAgaWYgKCFldmVudC5kZWZhdWx0UHJldmVudGVkICYmIG9uRGlzbWlzcykge1xuICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICBvbkRpc21pc3MoKTtcbiAgICAgIH1cbiAgICB9LCBvd25lckRvY3VtZW50KTtcblxuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBpZiAoIW5vZGUpIHJldHVybjtcbiAgICAgIGlmIChkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHMpIHtcbiAgICAgICAgaWYgKGNvbnRleHQubGF5ZXJzV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWQuc2l6ZSA9PT0gMCkge1xuICAgICAgICAgIG9yaWdpbmFsQm9keVBvaW50ZXJFdmVudHMgPSBvd25lckRvY3VtZW50LmJvZHkuc3R5bGUucG9pbnRlckV2ZW50cztcbiAgICAgICAgICBvd25lckRvY3VtZW50LmJvZHkuc3R5bGUucG9pbnRlckV2ZW50cyA9ICdub25lJztcbiAgICAgICAgfVxuICAgICAgICBjb250ZXh0LmxheWVyc1dpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkLmFkZChub2RlKTtcbiAgICAgIH1cbiAgICAgIGNvbnRleHQubGF5ZXJzLmFkZChub2RlKTtcbiAgICAgIGRpc3BhdGNoVXBkYXRlKCk7XG4gICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBpZiAoXG4gICAgICAgICAgZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzICYmXG4gICAgICAgICAgY29udGV4dC5sYXllcnNXaXRoT3V0c2lkZVBvaW50ZXJFdmVudHNEaXNhYmxlZC5zaXplID09PSAxXG4gICAgICAgICkge1xuICAgICAgICAgIG93bmVyRG9jdW1lbnQuYm9keS5zdHlsZS5wb2ludGVyRXZlbnRzID0gb3JpZ2luYWxCb2R5UG9pbnRlckV2ZW50cztcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICB9LCBbbm9kZSwgb3duZXJEb2N1bWVudCwgZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzLCBjb250ZXh0XSk7XG5cbiAgICAvKipcbiAgICAgKiBXZSBwdXJwb3NlZnVsbHkgcHJldmVudCBjb21iaW5pbmcgdGhpcyBlZmZlY3Qgd2l0aCB0aGUgYGRpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50c2AgZWZmZWN0XG4gICAgICogYmVjYXVzZSBhIGNoYW5nZSB0byBgZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzYCB3b3VsZCByZW1vdmUgdGhpcyBsYXllciBmcm9tIHRoZSBzdGFja1xuICAgICAqIGFuZCBhZGQgaXQgdG8gdGhlIGVuZCBhZ2FpbiBzbyB0aGUgbGF5ZXJpbmcgb3JkZXIgd291bGRuJ3QgYmUgX2NyZWF0aW9uIG9yZGVyXy5cbiAgICAgKiBXZSBvbmx5IHdhbnQgdGhlbSB0byBiZSByZW1vdmVkIGZyb20gY29udGV4dCBzdGFja3Mgd2hlbiB1bm1vdW50ZWQuXG4gICAgICovXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGlmICghbm9kZSkgcmV0dXJuO1xuICAgICAgICBjb250ZXh0LmxheWVycy5kZWxldGUobm9kZSk7XG4gICAgICAgIGNvbnRleHQubGF5ZXJzV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWQuZGVsZXRlKG5vZGUpO1xuICAgICAgICBkaXNwYXRjaFVwZGF0ZSgpO1xuICAgICAgfTtcbiAgICB9LCBbbm9kZSwgY29udGV4dF0pO1xuXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGNvbnN0IGhhbmRsZVVwZGF0ZSA9ICgpID0+IGZvcmNlKHt9KTtcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoQ09OVEVYVF9VUERBVEUsIGhhbmRsZVVwZGF0ZSk7XG4gICAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihDT05URVhUX1VQREFURSwgaGFuZGxlVXBkYXRlKTtcbiAgICB9LCBbXSk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPFByaW1pdGl2ZS5kaXZcbiAgICAgICAgey4uLmxheWVyUHJvcHN9XG4gICAgICAgIHJlZj17Y29tcG9zZWRSZWZzfVxuICAgICAgICBzdHlsZT17e1xuICAgICAgICAgIHBvaW50ZXJFdmVudHM6IGlzQm9keVBvaW50ZXJFdmVudHNEaXNhYmxlZFxuICAgICAgICAgICAgPyBpc1BvaW50ZXJFdmVudHNFbmFibGVkXG4gICAgICAgICAgICAgID8gJ2F1dG8nXG4gICAgICAgICAgICAgIDogJ25vbmUnXG4gICAgICAgICAgICA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAuLi5wcm9wcy5zdHlsZSxcbiAgICAgICAgfX1cbiAgICAgICAgb25Gb2N1c0NhcHR1cmU9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uRm9jdXNDYXB0dXJlLCBmb2N1c091dHNpZGUub25Gb2N1c0NhcHR1cmUpfVxuICAgICAgICBvbkJsdXJDYXB0dXJlPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbkJsdXJDYXB0dXJlLCBmb2N1c091dHNpZGUub25CbHVyQ2FwdHVyZSl9XG4gICAgICAgIG9uUG9pbnRlckRvd25DYXB0dXJlPXtjb21wb3NlRXZlbnRIYW5kbGVycyhcbiAgICAgICAgICBwcm9wcy5vblBvaW50ZXJEb3duQ2FwdHVyZSxcbiAgICAgICAgICBwb2ludGVyRG93bk91dHNpZGUub25Qb2ludGVyRG93bkNhcHR1cmVcbiAgICAgICAgKX1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuKTtcblxuRGlzbWlzc2FibGVMYXllci5kaXNwbGF5TmFtZSA9IERJU01JU1NBQkxFX0xBWUVSX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIERpc21pc3NhYmxlTGF5ZXJCcmFuY2hcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgQlJBTkNIX05BTUUgPSAnRGlzbWlzc2FibGVMYXllckJyYW5jaCc7XG5cbnR5cGUgRGlzbWlzc2FibGVMYXllckJyYW5jaEVsZW1lbnQgPSBSZWFjdC5Db21wb25lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIERpc21pc3NhYmxlTGF5ZXJCcmFuY2hQcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzIHt9XG5cbmNvbnN0IERpc21pc3NhYmxlTGF5ZXJCcmFuY2ggPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBEaXNtaXNzYWJsZUxheWVyQnJhbmNoRWxlbWVudCxcbiAgRGlzbWlzc2FibGVMYXllckJyYW5jaFByb3BzXG4+KChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KERpc21pc3NhYmxlTGF5ZXJDb250ZXh0KTtcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmPERpc21pc3NhYmxlTGF5ZXJCcmFuY2hFbGVtZW50PihudWxsKTtcbiAgY29uc3QgY29tcG9zZWRSZWZzID0gdXNlQ29tcG9zZWRSZWZzKGZvcndhcmRlZFJlZiwgcmVmKTtcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IG5vZGUgPSByZWYuY3VycmVudDtcbiAgICBpZiAobm9kZSkge1xuICAgICAgY29udGV4dC5icmFuY2hlcy5hZGQobm9kZSk7XG4gICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBjb250ZXh0LmJyYW5jaGVzLmRlbGV0ZShub2RlKTtcbiAgICAgIH07XG4gICAgfVxuICB9LCBbY29udGV4dC5icmFuY2hlc10pO1xuXG4gIHJldHVybiA8UHJpbWl0aXZlLmRpdiB7Li4ucHJvcHN9IHJlZj17Y29tcG9zZWRSZWZzfSAvPjtcbn0pO1xuXG5EaXNtaXNzYWJsZUxheWVyQnJhbmNoLmRpc3BsYXlOYW1lID0gQlJBTkNIX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxudHlwZSBQb2ludGVyRG93bk91dHNpZGVFdmVudCA9IEN1c3RvbUV2ZW50PHsgb3JpZ2luYWxFdmVudDogUG9pbnRlckV2ZW50IH0+O1xudHlwZSBGb2N1c091dHNpZGVFdmVudCA9IEN1c3RvbUV2ZW50PHsgb3JpZ2luYWxFdmVudDogRm9jdXNFdmVudCB9PjtcblxuLyoqXG4gKiBMaXN0ZW5zIGZvciBgcG9pbnRlcmRvd25gIG91dHNpZGUgYSByZWFjdCBzdWJ0cmVlLiBXZSB1c2UgYHBvaW50ZXJkb3duYCByYXRoZXIgdGhhbiBgcG9pbnRlcnVwYFxuICogdG8gbWltaWMgbGF5ZXIgZGlzbWlzc2luZyBiZWhhdmlvdXIgcHJlc2VudCBpbiBPUy5cbiAqIFJldHVybnMgcHJvcHMgdG8gcGFzcyB0byB0aGUgbm9kZSB3ZSB3YW50IHRvIGNoZWNrIGZvciBvdXRzaWRlIGV2ZW50cy5cbiAqL1xuZnVuY3Rpb24gdXNlUG9pbnRlckRvd25PdXRzaWRlKFxuICBvblBvaW50ZXJEb3duT3V0c2lkZT86IChldmVudDogUG9pbnRlckRvd25PdXRzaWRlRXZlbnQpID0+IHZvaWQsXG4gIG93bmVyRG9jdW1lbnQ6IERvY3VtZW50ID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnRcbikge1xuICBjb25zdCBoYW5kbGVQb2ludGVyRG93bk91dHNpZGUgPSB1c2VDYWxsYmFja1JlZihvblBvaW50ZXJEb3duT3V0c2lkZSkgYXMgRXZlbnRMaXN0ZW5lcjtcbiAgY29uc3QgaXNQb2ludGVySW5zaWRlUmVhY3RUcmVlUmVmID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcbiAgY29uc3QgaGFuZGxlQ2xpY2tSZWYgPSBSZWFjdC51c2VSZWYoKCkgPT4ge30pO1xuXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlUG9pbnRlckRvd24gPSAoZXZlbnQ6IFBvaW50ZXJFdmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LnRhcmdldCAmJiAhaXNQb2ludGVySW5zaWRlUmVhY3RUcmVlUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgY29uc3QgZXZlbnREZXRhaWwgPSB7IG9yaWdpbmFsRXZlbnQ6IGV2ZW50IH07XG5cbiAgICAgICAgZnVuY3Rpb24gaGFuZGxlQW5kRGlzcGF0Y2hQb2ludGVyRG93bk91dHNpZGVFdmVudCgpIHtcbiAgICAgICAgICBoYW5kbGVBbmREaXNwYXRjaEN1c3RvbUV2ZW50KFxuICAgICAgICAgICAgUE9JTlRFUl9ET1dOX09VVFNJREUsXG4gICAgICAgICAgICBoYW5kbGVQb2ludGVyRG93bk91dHNpZGUsXG4gICAgICAgICAgICBldmVudERldGFpbCxcbiAgICAgICAgICAgIHsgZGlzY3JldGU6IHRydWUgfVxuICAgICAgICAgICk7XG4gICAgICAgIH1cblxuICAgICAgICAvKipcbiAgICAgICAgICogT24gdG91Y2ggZGV2aWNlcywgd2UgbmVlZCB0byB3YWl0IGZvciBhIGNsaWNrIGV2ZW50IGJlY2F1c2UgYnJvd3NlcnMgaW1wbGVtZW50XG4gICAgICAgICAqIGEgfjM1MG1zIGRlbGF5IGJldHdlZW4gdGhlIHRpbWUgdGhlIHVzZXIgc3RvcHMgdG91Y2hpbmcgdGhlIGRpc3BsYXkgYW5kIHdoZW4gdGhlXG4gICAgICAgICAqIGJyb3dzZXIgZXhlY3V0cmVzIGV2ZW50cy4gV2UgbmVlZCB0byBlbnN1cmUgd2UgZG9uJ3QgcmVhY3RpdmF0ZSBwb2ludGVyLWV2ZW50cyB3aXRoaW5cbiAgICAgICAgICogdGhpcyB0aW1lZnJhbWUgb3RoZXJ3aXNlIHRoZSBicm93c2VyIG1heSBleGVjdXRlIGV2ZW50cyB0aGF0IHNob3VsZCBoYXZlIGJlZW4gcHJldmVudGVkLlxuICAgICAgICAgKlxuICAgICAgICAgKiBBZGRpdGlvbmFsbHksIHRoaXMgYWxzbyBsZXRzIHVzIGRlYWwgYXV0b21hdGljYWxseSB3aXRoIGNhbmNlbGxhdGlvbnMgd2hlbiBhIGNsaWNrIGV2ZW50XG4gICAgICAgICAqIGlzbid0IHJhaXNlZCBiZWNhdXNlIHRoZSBwYWdlIHdhcyBjb25zaWRlcmVkIHNjcm9sbGVkL2RyYWctc2Nyb2xsZWQsIGxvbmctcHJlc3NlZCwgZXRjLlxuICAgICAgICAgKlxuICAgICAgICAgKiBUaGlzIGlzIHdoeSB3ZSBhbHNvIGNvbnRpbnVvdXNseSByZW1vdmUgdGhlIHByZXZpb3VzIGxpc3RlbmVyLCBiZWNhdXNlIHdlIGNhbm5vdCBiZVxuICAgICAgICAgKiBjZXJ0YWluIHRoYXQgaXQgd2FzIHJhaXNlZCwgYW5kIHRoZXJlZm9yZSBjbGVhbmVkLXVwLlxuICAgICAgICAgKi9cbiAgICAgICAgaWYgKGV2ZW50LnBvaW50ZXJUeXBlID09PSAndG91Y2gnKSB7XG4gICAgICAgICAgb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGljaycsIGhhbmRsZUNsaWNrUmVmLmN1cnJlbnQpO1xuICAgICAgICAgIGhhbmRsZUNsaWNrUmVmLmN1cnJlbnQgPSBoYW5kbGVBbmREaXNwYXRjaFBvaW50ZXJEb3duT3V0c2lkZUV2ZW50O1xuICAgICAgICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVDbGlja1JlZi5jdXJyZW50LCB7IG9uY2U6IHRydWUgfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hQb2ludGVyRG93bk91dHNpZGVFdmVudCgpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBXZSBuZWVkIHRvIHJlbW92ZSB0aGUgZXZlbnQgbGlzdGVuZXIgaW4gY2FzZSB0aGUgb3V0c2lkZSBjbGljayBoYXMgYmVlbiBjYW5jZWxlZC5cbiAgICAgICAgLy8gU2VlOiBodHRwczovL2dpdGh1Yi5jb20vcmFkaXgtdWkvcHJpbWl0aXZlcy9pc3N1ZXMvMjE3MVxuICAgICAgICBvd25lckRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgaGFuZGxlQ2xpY2tSZWYuY3VycmVudCk7XG4gICAgICB9XG4gICAgICBpc1BvaW50ZXJJbnNpZGVSZWFjdFRyZWVSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogaWYgdGhpcyBob29rIGV4ZWN1dGVzIGluIGEgY29tcG9uZW50IHRoYXQgbW91bnRzIHZpYSBhIGBwb2ludGVyZG93bmAgZXZlbnQsIHRoZSBldmVudFxuICAgICAqIHdvdWxkIGJ1YmJsZSB1cCB0byB0aGUgZG9jdW1lbnQgYW5kIHRyaWdnZXIgYSBgcG9pbnRlckRvd25PdXRzaWRlYCBldmVudC4gV2UgYXZvaWRcbiAgICAgKiB0aGlzIGJ5IGRlbGF5aW5nIHRoZSBldmVudCBsaXN0ZW5lciByZWdpc3RyYXRpb24gb24gdGhlIGRvY3VtZW50LlxuICAgICAqIFRoaXMgaXMgbm90IFJlYWN0IHNwZWNpZmljLCBidXQgcmF0aGVyIGhvdyB0aGUgRE9NIHdvcmtzLCBpZTpcbiAgICAgKiBgYGBcbiAgICAgKiBidXR0b24uYWRkRXZlbnRMaXN0ZW5lcigncG9pbnRlcmRvd24nLCAoKSA9PiB7XG4gICAgICogICBjb25zb2xlLmxvZygnSSB3aWxsIGxvZycpO1xuICAgICAqICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigncG9pbnRlcmRvd24nLCAoKSA9PiB7XG4gICAgICogICAgIGNvbnNvbGUubG9nKCdJIHdpbGwgYWxzbyBsb2cnKTtcbiAgICAgKiAgIH0pXG4gICAgICogfSk7XG4gICAgICovXG4gICAgY29uc3QgdGltZXJJZCA9IHdpbmRvdy5zZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigncG9pbnRlcmRvd24nLCBoYW5kbGVQb2ludGVyRG93bik7XG4gICAgfSwgMCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHdpbmRvdy5jbGVhclRpbWVvdXQodGltZXJJZCk7XG4gICAgICBvd25lckRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJkb3duJywgaGFuZGxlUG9pbnRlckRvd24pO1xuICAgICAgb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGljaycsIGhhbmRsZUNsaWNrUmVmLmN1cnJlbnQpO1xuICAgIH07XG4gIH0sIFtvd25lckRvY3VtZW50LCBoYW5kbGVQb2ludGVyRG93bk91dHNpZGVdKTtcblxuICByZXR1cm4ge1xuICAgIC8vIGVuc3VyZXMgd2UgY2hlY2sgUmVhY3QgY29tcG9uZW50IHRyZWUgKG5vdCBqdXN0IERPTSB0cmVlKVxuICAgIG9uUG9pbnRlckRvd25DYXB0dXJlOiAoKSA9PiAoaXNQb2ludGVySW5zaWRlUmVhY3RUcmVlUmVmLmN1cnJlbnQgPSB0cnVlKSxcbiAgfTtcbn1cblxuLyoqXG4gKiBMaXN0ZW5zIGZvciB3aGVuIGZvY3VzIGhhcHBlbnMgb3V0c2lkZSBhIHJlYWN0IHN1YnRyZWUuXG4gKiBSZXR1cm5zIHByb3BzIHRvIHBhc3MgdG8gdGhlIHJvb3QgKG5vZGUpIG9mIHRoZSBzdWJ0cmVlIHdlIHdhbnQgdG8gY2hlY2suXG4gKi9cbmZ1bmN0aW9uIHVzZUZvY3VzT3V0c2lkZShcbiAgb25Gb2N1c091dHNpZGU/OiAoZXZlbnQ6IEZvY3VzT3V0c2lkZUV2ZW50KSA9PiB2b2lkLFxuICBvd25lckRvY3VtZW50OiBEb2N1bWVudCA9IGdsb2JhbFRoaXM/LmRvY3VtZW50XG4pIHtcbiAgY29uc3QgaGFuZGxlRm9jdXNPdXRzaWRlID0gdXNlQ2FsbGJhY2tSZWYob25Gb2N1c091dHNpZGUpIGFzIEV2ZW50TGlzdGVuZXI7XG4gIGNvbnN0IGlzRm9jdXNJbnNpZGVSZWFjdFRyZWVSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlRm9jdXMgPSAoZXZlbnQ6IEZvY3VzRXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC50YXJnZXQgJiYgIWlzRm9jdXNJbnNpZGVSZWFjdFRyZWVSZWYuY3VycmVudCkge1xuICAgICAgICBjb25zdCBldmVudERldGFpbCA9IHsgb3JpZ2luYWxFdmVudDogZXZlbnQgfTtcbiAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudChGT0NVU19PVVRTSURFLCBoYW5kbGVGb2N1c091dHNpZGUsIGV2ZW50RGV0YWlsLCB7XG4gICAgICAgICAgZGlzY3JldGU6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9O1xuICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignZm9jdXNpbicsIGhhbmRsZUZvY3VzKTtcbiAgICByZXR1cm4gKCkgPT4gb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdmb2N1c2luJywgaGFuZGxlRm9jdXMpO1xuICB9LCBbb3duZXJEb2N1bWVudCwgaGFuZGxlRm9jdXNPdXRzaWRlXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBvbkZvY3VzQ2FwdHVyZTogKCkgPT4gKGlzRm9jdXNJbnNpZGVSZWFjdFRyZWVSZWYuY3VycmVudCA9IHRydWUpLFxuICAgIG9uQmx1ckNhcHR1cmU6ICgpID0+IChpc0ZvY3VzSW5zaWRlUmVhY3RUcmVlUmVmLmN1cnJlbnQgPSBmYWxzZSksXG4gIH07XG59XG5cbmZ1bmN0aW9uIGRpc3BhdGNoVXBkYXRlKCkge1xuICBjb25zdCBldmVudCA9IG5ldyBDdXN0b21FdmVudChDT05URVhUX1VQREFURSk7XG4gIGRvY3VtZW50LmRpc3BhdGNoRXZlbnQoZXZlbnQpO1xufVxuXG5mdW5jdGlvbiBoYW5kbGVBbmREaXNwYXRjaEN1c3RvbUV2ZW50PEUgZXh0ZW5kcyBDdXN0b21FdmVudCwgT3JpZ2luYWxFdmVudCBleHRlbmRzIEV2ZW50PihcbiAgbmFtZTogc3RyaW5nLFxuICBoYW5kbGVyOiAoKGV2ZW50OiBFKSA9PiB2b2lkKSB8IHVuZGVmaW5lZCxcbiAgZGV0YWlsOiB7IG9yaWdpbmFsRXZlbnQ6IE9yaWdpbmFsRXZlbnQgfSAmIChFIGV4dGVuZHMgQ3VzdG9tRXZlbnQ8aW5mZXIgRD4gPyBEIDogbmV2ZXIpLFxuICB7IGRpc2NyZXRlIH06IHsgZGlzY3JldGU6IGJvb2xlYW4gfVxuKSB7XG4gIGNvbnN0IHRhcmdldCA9IGRldGFpbC5vcmlnaW5hbEV2ZW50LnRhcmdldDtcbiAgY29uc3QgZXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQobmFtZSwgeyBidWJibGVzOiBmYWxzZSwgY2FuY2VsYWJsZTogdHJ1ZSwgZGV0YWlsIH0pO1xuICBpZiAoaGFuZGxlcikgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIobmFtZSwgaGFuZGxlciBhcyBFdmVudExpc3RlbmVyLCB7IG9uY2U6IHRydWUgfSk7XG5cbiAgaWYgKGRpc2NyZXRlKSB7XG4gICAgZGlzcGF0Y2hEaXNjcmV0ZUN1c3RvbUV2ZW50KHRhcmdldCwgZXZlbnQpO1xuICB9IGVsc2Uge1xuICAgIHRhcmdldC5kaXNwYXRjaEV2ZW50KGV2ZW50KTtcbiAgfVxufVxuXG5jb25zdCBSb290ID0gRGlzbWlzc2FibGVMYXllcjtcbmNvbnN0IEJyYW5jaCA9IERpc21pc3NhYmxlTGF5ZXJCcmFuY2g7XG5cbmV4cG9ydCB7XG4gIERpc21pc3NhYmxlTGF5ZXIsXG4gIERpc21pc3NhYmxlTGF5ZXJCcmFuY2gsXG4gIC8vXG4gIFJvb3QsXG4gIEJyYW5jaCxcbn07XG5leHBvcnQgdHlwZSB7IERpc21pc3NhYmxlTGF5ZXJQcm9wcyB9O1xuIl0sIm5hbWVzIjpbIm5vZGUiLCJoYW5kbGVBbmREaXNwYXRjaFBvaW50ZXJEb3duT3V0c2lkZUV2ZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNxQztBQUNwRSxpQkFBaUIseUxBQUs7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQiwyQ0FBYztBQUNwQyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILDJDQUEyQyxHQUFHO0FBQzlDO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3J5YW53b25nL2R5YWQtYXBwcy92aWJlY29kZWJhc2Uvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1pZC9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9pZC9zcmMvaWQudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3RcIjtcbnZhciB1c2VSZWFjdElkID0gUmVhY3RbXCIgdXNlSWQgXCIudHJpbSgpLnRvU3RyaW5nKCldIHx8ICgoKSA9PiB2b2lkIDApO1xudmFyIGNvdW50ID0gMDtcbmZ1bmN0aW9uIHVzZUlkKGRldGVybWluaXN0aWNJZCkge1xuICBjb25zdCBbaWQsIHNldElkXSA9IFJlYWN0LnVzZVN0YXRlKHVzZVJlYWN0SWQoKSk7XG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFkZXRlcm1pbmlzdGljSWQpIHNldElkKChyZWFjdElkKSA9PiByZWFjdElkID8/IFN0cmluZyhjb3VudCsrKSk7XG4gIH0sIFtkZXRlcm1pbmlzdGljSWRdKTtcbiAgcmV0dXJuIGRldGVybWluaXN0aWNJZCB8fCAoaWQgPyBgcmFkaXgtJHtpZH1gIDogXCJcIik7XG59XG5leHBvcnQge1xuICB1c2VJZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"PopperAnchor.useEffect\": ()=>{\n            context.onAnchorChange(virtualRef?.current || ref.current);\n        }\n    }[\"PopperAnchor.useEffect\"]);\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"PopperContent.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"PopperContent.useComposedRefs[composedRefs]\"]);\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: {\n            \"PopperContent.useFloating\": (...args)=>{\n                const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                    animationFrame: updatePositionStrategy === \"always\"\n                });\n                return cleanup;\n            }\n        }[\"PopperContent.useFloating\"],\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: {\n                    \"PopperContent.useFloating\": ({ elements, rects, availableWidth, availableHeight })=>{\n                        const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                        const contentStyle = elements.floating.style;\n                        contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                    }\n                }[\"PopperContent.useFloating\"]\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (isPositioned) {\n                handlePlaced?.();\n            }\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)({\n        \"Portal.useLayoutEffect\": ()=>setMounted(true)\n    }[\"Portal.useLayoutEffect\"], []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXBvcnRhbC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF1QjtBQUNGO0FBQ0s7QUFDTTtBQXVCSjtBQWpCNUIsSUFBTSxjQUFjO0FBV3BCLElBQU0sdUJBQWUsOENBQXVDLENBQUMsT0FBTztJQUNsRSxNQUFNLEVBQUUsV0FBVyxlQUFlLEdBQUcsWUFBWSxJQUFJO0lBQ3JELE1BQU0sQ0FBQyxTQUFTLFVBQVUsSUFBVSw0Q0FBUyxLQUFLO0lBQ2xELGtGQUFlO2tDQUFDLElBQU0sV0FBVyxJQUFJO2lDQUFHLENBQUMsQ0FBQztJQUMxQyxNQUFNLFlBQVksaUJBQWtCLFdBQVcsWUFBWSxVQUFVO0lBQ3JFLE9BQU8sMEJBQ0gsbURBQVMsQ0FBYSx1RUFBQyxnRUFBUyxDQUFDLEtBQVY7UUFBZSxHQUFHO1FBQWEsS0FBSztJQUFBLENBQWMsR0FBSSxTQUFTLElBQ3RGO0FBQ04sQ0FBQztBQUVELE9BQU8sY0FBYztBQUlyQixJQUFNLE9BQU8iLCJzb3VyY2VzIjpbIi9Vc2Vycy9yeWFud29uZy9keWFkLWFwcHMvc3JjL3BvcnRhbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFJlYWN0RE9NIGZyb20gJ3JlYWN0LWRvbSc7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlJztcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdCc7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFBvcnRhbFxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBQT1JUQUxfTkFNRSA9ICdQb3J0YWwnO1xuXG50eXBlIFBvcnRhbEVsZW1lbnQgPSBSZWFjdC5Db21wb25lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xudHlwZSBQcmltaXRpdmVEaXZQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLmRpdj47XG5pbnRlcmZhY2UgUG9ydGFsUHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVEaXZQcm9wcyB7XG4gIC8qKlxuICAgKiBBbiBvcHRpb25hbCBjb250YWluZXIgd2hlcmUgdGhlIHBvcnRhbGVkIGNvbnRlbnQgc2hvdWxkIGJlIGFwcGVuZGVkLlxuICAgKi9cbiAgY29udGFpbmVyPzogRWxlbWVudCB8IERvY3VtZW50RnJhZ21lbnQgfCBudWxsO1xufVxuXG5jb25zdCBQb3J0YWwgPSBSZWFjdC5mb3J3YXJkUmVmPFBvcnRhbEVsZW1lbnQsIFBvcnRhbFByb3BzPigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICBjb25zdCB7IGNvbnRhaW5lcjogY29udGFpbmVyUHJvcCwgLi4ucG9ydGFsUHJvcHMgfSA9IHByb3BzO1xuICBjb25zdCBbbW91bnRlZCwgc2V0TW91bnRlZF0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiBzZXRNb3VudGVkKHRydWUpLCBbXSk7XG4gIGNvbnN0IGNvbnRhaW5lciA9IGNvbnRhaW5lclByb3AgfHwgKG1vdW50ZWQgJiYgZ2xvYmFsVGhpcz8uZG9jdW1lbnQ/LmJvZHkpO1xuICByZXR1cm4gY29udGFpbmVyXG4gICAgPyBSZWFjdERPTS5jcmVhdGVQb3J0YWwoPFByaW1pdGl2ZS5kaXYgey4uLnBvcnRhbFByb3BzfSByZWY9e2ZvcndhcmRlZFJlZn0gLz4sIGNvbnRhaW5lcilcbiAgICA6IG51bGw7XG59KTtcblxuUG9ydGFsLmRpc3BsYXlOYW1lID0gUE9SVEFMX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgUm9vdCA9IFBvcnRhbDtcblxuZXhwb3J0IHtcbiAgUG9ydGFsLFxuICAvL1xuICBSb290LFxufTtcbmV4cG9ydCB0eXBlIHsgUG9ydGFsUHJvcHMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePresence.useEffect\": ()=>{\n            const currentAnimationName = getAnimationName(stylesRef.current);\n            prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n        }\n    }[\"usePresence.useEffect\"], [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            const styles = stylesRef.current;\n            const wasPresent = prevPresentRef.current;\n            const hasPresentChanged = wasPresent !== present;\n            if (hasPresentChanged) {\n                const prevAnimationName = prevAnimationNameRef.current;\n                const currentAnimationName = getAnimationName(styles);\n                if (present) {\n                    send(\"MOUNT\");\n                } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                    send(\"UNMOUNT\");\n                } else {\n                    const isAnimating = prevAnimationName !== currentAnimationName;\n                    if (wasPresent && isAnimating) {\n                        send(\"ANIMATION_OUT\");\n                    } else {\n                        send(\"UNMOUNT\");\n                    }\n                }\n                prevPresentRef.current = present;\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            if (node) {\n                let timeoutId;\n                const ownerWindow = node.ownerDocument.defaultView ?? window;\n                const handleAnimationEnd = {\n                    \"usePresence.useLayoutEffect.handleAnimationEnd\": (event)=>{\n                        const currentAnimationName = getAnimationName(stylesRef.current);\n                        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                        if (event.target === node && isCurrentAnimation) {\n                            send(\"ANIMATION_END\");\n                            if (!prevPresentRef.current) {\n                                const currentFillMode = node.style.animationFillMode;\n                                node.style.animationFillMode = \"forwards\";\n                                timeoutId = ownerWindow.setTimeout({\n                                    \"usePresence.useLayoutEffect.handleAnimationEnd\": ()=>{\n                                        if (node.style.animationFillMode === \"forwards\") {\n                                            node.style.animationFillMode = currentFillMode;\n                                        }\n                                    }\n                                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"]);\n                            }\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"];\n                const handleAnimationStart = {\n                    \"usePresence.useLayoutEffect.handleAnimationStart\": (event)=>{\n                        if (event.target === node) {\n                            prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationStart\"];\n                node.addEventListener(\"animationstart\", handleAnimationStart);\n                node.addEventListener(\"animationcancel\", handleAnimationEnd);\n                node.addEventListener(\"animationend\", handleAnimationEnd);\n                return ({\n                    \"usePresence.useLayoutEffect\": ()=>{\n                        ownerWindow.clearTimeout(timeoutId);\n                        node.removeEventListener(\"animationstart\", handleAnimationStart);\n                        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                        node.removeEventListener(\"animationend\", handleAnimationEnd);\n                    }\n                })[\"usePresence.useLayoutEffect\"];\n            } else {\n                send(\"ANIMATION_END\");\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"usePresence.useCallback\": (node2)=>{\n                stylesRef.current = node2 ? getComputedStyle(node2) : null;\n                setNode(node2);\n            }\n        }[\"usePresence.useCallback\"], [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXNsb3QvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQTtBQUMrQjtBQUM0QjtBQUNJO0FBQy9EO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw2Q0FBZ0I7QUFDaEMsWUFBWSx5QkFBeUI7QUFDckMsMEJBQTBCLDJDQUFjO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLDJDQUFjLCtCQUErQiwyQ0FBYztBQUN6RSxpQkFBaUIsaURBQW9CO0FBQ3JDLFVBQVU7QUFDVjtBQUNBO0FBQ0EsT0FBTztBQUNQLDZCQUE2QixzREFBRyxjQUFjLDJDQUEyQyxpREFBb0IsZUFBZSwrQ0FBa0IsMENBQTBDO0FBQ3hMO0FBQ0EsMkJBQTJCLHNEQUFHLGNBQWMsMkNBQTJDO0FBQ3ZGLEdBQUc7QUFDSCx5QkFBeUIsVUFBVTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDZDQUFnQjtBQUNwQyxZQUFZLHlCQUF5QjtBQUNyQyxRQUFRLGlEQUFvQjtBQUM1QjtBQUNBO0FBQ0EsNEJBQTRCLDJDQUFjO0FBQzFDLG9DQUFvQyx5RUFBVztBQUMvQztBQUNBLGFBQWEsK0NBQWtCO0FBQy9CO0FBQ0EsV0FBVywyQ0FBYyx1QkFBdUIsMkNBQWM7QUFDOUQsR0FBRztBQUNILDZCQUE2QixVQUFVO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVTtBQUNsQywyQkFBMkIsc0RBQUcsQ0FBQyx1REFBUyxJQUFJLFVBQVU7QUFDdEQ7QUFDQSw4QkFBOEIsVUFBVTtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxpREFBb0I7QUFDN0I7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxNQUFNO0FBQ04sa0NBQWtDO0FBQ2xDLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQU9FO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yeWFud29uZy9keWFkLWFwcHMvdmliZWNvZGViYXNlL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3Qtc2xvdC9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvc2xvdC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgY29tcG9zZVJlZnMgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmc1wiO1xuaW1wb3J0IHsgRnJhZ21lbnQgYXMgRnJhZ21lbnQyLCBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbi8vIEBfX05PX1NJREVfRUZGRUNUU19fXG5mdW5jdGlvbiBjcmVhdGVTbG90KG93bmVyTmFtZSkge1xuICBjb25zdCBTbG90Q2xvbmUgPSAvKiBAX19QVVJFX18gKi8gY3JlYXRlU2xvdENsb25lKG93bmVyTmFtZSk7XG4gIGNvbnN0IFNsb3QyID0gUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgY2hpbGRyZW4sIC4uLnNsb3RQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgY2hpbGRyZW5BcnJheSA9IFJlYWN0LkNoaWxkcmVuLnRvQXJyYXkoY2hpbGRyZW4pO1xuICAgIGNvbnN0IHNsb3R0YWJsZSA9IGNoaWxkcmVuQXJyYXkuZmluZChpc1Nsb3R0YWJsZSk7XG4gICAgaWYgKHNsb3R0YWJsZSkge1xuICAgICAgY29uc3QgbmV3RWxlbWVudCA9IHNsb3R0YWJsZS5wcm9wcy5jaGlsZHJlbjtcbiAgICAgIGNvbnN0IG5ld0NoaWxkcmVuID0gY2hpbGRyZW5BcnJheS5tYXAoKGNoaWxkKSA9PiB7XG4gICAgICAgIGlmIChjaGlsZCA9PT0gc2xvdHRhYmxlKSB7XG4gICAgICAgICAgaWYgKFJlYWN0LkNoaWxkcmVuLmNvdW50KG5ld0VsZW1lbnQpID4gMSkgcmV0dXJuIFJlYWN0LkNoaWxkcmVuLm9ubHkobnVsbCk7XG4gICAgICAgICAgcmV0dXJuIFJlYWN0LmlzVmFsaWRFbGVtZW50KG5ld0VsZW1lbnQpID8gbmV3RWxlbWVudC5wcm9wcy5jaGlsZHJlbiA6IG51bGw7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuIGNoaWxkO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFNsb3RDbG9uZSwgeyAuLi5zbG90UHJvcHMsIHJlZjogZm9yd2FyZGVkUmVmLCBjaGlsZHJlbjogUmVhY3QuaXNWYWxpZEVsZW1lbnQobmV3RWxlbWVudCkgPyBSZWFjdC5jbG9uZUVsZW1lbnQobmV3RWxlbWVudCwgdm9pZCAwLCBuZXdDaGlsZHJlbikgOiBudWxsIH0pO1xuICAgIH1cbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChTbG90Q2xvbmUsIHsgLi4uc2xvdFByb3BzLCByZWY6IGZvcndhcmRlZFJlZiwgY2hpbGRyZW4gfSk7XG4gIH0pO1xuICBTbG90Mi5kaXNwbGF5TmFtZSA9IGAke293bmVyTmFtZX0uU2xvdGA7XG4gIHJldHVybiBTbG90Mjtcbn1cbnZhciBTbG90ID0gLyogQF9fUFVSRV9fICovIGNyZWF0ZVNsb3QoXCJTbG90XCIpO1xuLy8gQF9fTk9fU0lERV9FRkZFQ1RTX19cbmZ1bmN0aW9uIGNyZWF0ZVNsb3RDbG9uZShvd25lck5hbWUpIHtcbiAgY29uc3QgU2xvdENsb25lID0gUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgY2hpbGRyZW4sIC4uLnNsb3RQcm9wcyB9ID0gcHJvcHM7XG4gICAgaWYgKFJlYWN0LmlzVmFsaWRFbGVtZW50KGNoaWxkcmVuKSkge1xuICAgICAgY29uc3QgY2hpbGRyZW5SZWYgPSBnZXRFbGVtZW50UmVmKGNoaWxkcmVuKTtcbiAgICAgIGNvbnN0IHByb3BzMiA9IG1lcmdlUHJvcHMoc2xvdFByb3BzLCBjaGlsZHJlbi5wcm9wcyk7XG4gICAgICBpZiAoY2hpbGRyZW4udHlwZSAhPT0gUmVhY3QuRnJhZ21lbnQpIHtcbiAgICAgICAgcHJvcHMyLnJlZiA9IGZvcndhcmRlZFJlZiA/IGNvbXBvc2VSZWZzKGZvcndhcmRlZFJlZiwgY2hpbGRyZW5SZWYpIDogY2hpbGRyZW5SZWY7XG4gICAgICB9XG4gICAgICByZXR1cm4gUmVhY3QuY2xvbmVFbGVtZW50KGNoaWxkcmVuLCBwcm9wczIpO1xuICAgIH1cbiAgICByZXR1cm4gUmVhY3QuQ2hpbGRyZW4uY291bnQoY2hpbGRyZW4pID4gMSA/IFJlYWN0LkNoaWxkcmVuLm9ubHkobnVsbCkgOiBudWxsO1xuICB9KTtcbiAgU2xvdENsb25lLmRpc3BsYXlOYW1lID0gYCR7b3duZXJOYW1lfS5TbG90Q2xvbmVgO1xuICByZXR1cm4gU2xvdENsb25lO1xufVxudmFyIFNMT1RUQUJMRV9JREVOVElGSUVSID0gU3ltYm9sKFwicmFkaXguc2xvdHRhYmxlXCIpO1xuLy8gQF9fTk9fU0lERV9FRkZFQ1RTX19cbmZ1bmN0aW9uIGNyZWF0ZVNsb3R0YWJsZShvd25lck5hbWUpIHtcbiAgY29uc3QgU2xvdHRhYmxlMiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChGcmFnbWVudDIsIHsgY2hpbGRyZW4gfSk7XG4gIH07XG4gIFNsb3R0YWJsZTIuZGlzcGxheU5hbWUgPSBgJHtvd25lck5hbWV9LlNsb3R0YWJsZWA7XG4gIFNsb3R0YWJsZTIuX19yYWRpeElkID0gU0xPVFRBQkxFX0lERU5USUZJRVI7XG4gIHJldHVybiBTbG90dGFibGUyO1xufVxudmFyIFNsb3R0YWJsZSA9IC8qIEBfX1BVUkVfXyAqLyBjcmVhdGVTbG90dGFibGUoXCJTbG90dGFibGVcIik7XG5mdW5jdGlvbiBpc1Nsb3R0YWJsZShjaGlsZCkge1xuICByZXR1cm4gUmVhY3QuaXNWYWxpZEVsZW1lbnQoY2hpbGQpICYmIHR5cGVvZiBjaGlsZC50eXBlID09PSBcImZ1bmN0aW9uXCIgJiYgXCJfX3JhZGl4SWRcIiBpbiBjaGlsZC50eXBlICYmIGNoaWxkLnR5cGUuX19yYWRpeElkID09PSBTTE9UVEFCTEVfSURFTlRJRklFUjtcbn1cbmZ1bmN0aW9uIG1lcmdlUHJvcHMoc2xvdFByb3BzLCBjaGlsZFByb3BzKSB7XG4gIGNvbnN0IG92ZXJyaWRlUHJvcHMgPSB7IC4uLmNoaWxkUHJvcHMgfTtcbiAgZm9yIChjb25zdCBwcm9wTmFtZSBpbiBjaGlsZFByb3BzKSB7XG4gICAgY29uc3Qgc2xvdFByb3BWYWx1ZSA9IHNsb3RQcm9wc1twcm9wTmFtZV07XG4gICAgY29uc3QgY2hpbGRQcm9wVmFsdWUgPSBjaGlsZFByb3BzW3Byb3BOYW1lXTtcbiAgICBjb25zdCBpc0hhbmRsZXIgPSAvXm9uW0EtWl0vLnRlc3QocHJvcE5hbWUpO1xuICAgIGlmIChpc0hhbmRsZXIpIHtcbiAgICAgIGlmIChzbG90UHJvcFZhbHVlICYmIGNoaWxkUHJvcFZhbHVlKSB7XG4gICAgICAgIG92ZXJyaWRlUHJvcHNbcHJvcE5hbWVdID0gKC4uLmFyZ3MpID0+IHtcbiAgICAgICAgICBjb25zdCByZXN1bHQgPSBjaGlsZFByb3BWYWx1ZSguLi5hcmdzKTtcbiAgICAgICAgICBzbG90UHJvcFZhbHVlKC4uLmFyZ3MpO1xuICAgICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICAgIH07XG4gICAgICB9IGVsc2UgaWYgKHNsb3RQcm9wVmFsdWUpIHtcbiAgICAgICAgb3ZlcnJpZGVQcm9wc1twcm9wTmFtZV0gPSBzbG90UHJvcFZhbHVlO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAocHJvcE5hbWUgPT09IFwic3R5bGVcIikge1xuICAgICAgb3ZlcnJpZGVQcm9wc1twcm9wTmFtZV0gPSB7IC4uLnNsb3RQcm9wVmFsdWUsIC4uLmNoaWxkUHJvcFZhbHVlIH07XG4gICAgfSBlbHNlIGlmIChwcm9wTmFtZSA9PT0gXCJjbGFzc05hbWVcIikge1xuICAgICAgb3ZlcnJpZGVQcm9wc1twcm9wTmFtZV0gPSBbc2xvdFByb3BWYWx1ZSwgY2hpbGRQcm9wVmFsdWVdLmZpbHRlcihCb29sZWFuKS5qb2luKFwiIFwiKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHsgLi4uc2xvdFByb3BzLCAuLi5vdmVycmlkZVByb3BzIH07XG59XG5mdW5jdGlvbiBnZXRFbGVtZW50UmVmKGVsZW1lbnQpIHtcbiAgbGV0IGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoZWxlbWVudC5wcm9wcywgXCJyZWZcIik/LmdldDtcbiAgbGV0IG1heVdhcm4gPSBnZXR0ZXIgJiYgXCJpc1JlYWN0V2FybmluZ1wiIGluIGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmc7XG4gIGlmIChtYXlXYXJuKSB7XG4gICAgcmV0dXJuIGVsZW1lbnQucmVmO1xuICB9XG4gIGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoZWxlbWVudCwgXCJyZWZcIik/LmdldDtcbiAgbWF5V2FybiA9IGdldHRlciAmJiBcImlzUmVhY3RXYXJuaW5nXCIgaW4gZ2V0dGVyICYmIGdldHRlci5pc1JlYWN0V2FybmluZztcbiAgaWYgKG1heVdhcm4pIHtcbiAgICByZXR1cm4gZWxlbWVudC5wcm9wcy5yZWY7XG4gIH1cbiAgcmV0dXJuIGVsZW1lbnQucHJvcHMucmVmIHx8IGVsZW1lbnQucmVmO1xufVxuZXhwb3J0IHtcbiAgU2xvdCBhcyBSb290LFxuICBTbG90LFxuICBTbG90dGFibGUsXG4gIGNyZWF0ZVNsb3QsXG4gIGNyZWF0ZVNsb3R0YWJsZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"ToastProvider.useCallback\": ()=>setToastCount({\n                        \"ToastProvider.useCallback\": (prevCount)=>prevCount + 1\n                    }[\"ToastProvider.useCallback\"])\n            }[\"ToastProvider.useCallback\"], []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"ToastProvider.useCallback\": ()=>setToastCount({\n                        \"ToastProvider.useCallback\": (prevCount)=>prevCount - 1\n                    }[\"ToastProvider.useCallback\"])\n            }[\"ToastProvider.useCallback\"], []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastViewport.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"ToastViewport.useEffect.handleKeyDown\": (event)=>{\n                    const isHotkeyPressed = hotkey.length !== 0 && hotkey.every({\n                        \"ToastViewport.useEffect.handleKeyDown\": (key)=>event[key] || event.code === key\n                    }[\"ToastViewport.useEffect.handleKeyDown\"]);\n                    if (isHotkeyPressed) ref.current?.focus();\n                }\n            }[\"ToastViewport.useEffect.handleKeyDown\"];\n            document.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"ToastViewport.useEffect\": ()=>document.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"ToastViewport.useEffect\"];\n        }\n    }[\"ToastViewport.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastViewport.useEffect\": ()=>{\n            const wrapper = wrapperRef.current;\n            const viewport = ref.current;\n            if (hasToasts && wrapper && viewport) {\n                const handlePause = {\n                    \"ToastViewport.useEffect.handlePause\": ()=>{\n                        if (!context.isClosePausedRef.current) {\n                            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                            viewport.dispatchEvent(pauseEvent);\n                            context.isClosePausedRef.current = true;\n                        }\n                    }\n                }[\"ToastViewport.useEffect.handlePause\"];\n                const handleResume = {\n                    \"ToastViewport.useEffect.handleResume\": ()=>{\n                        if (context.isClosePausedRef.current) {\n                            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                            viewport.dispatchEvent(resumeEvent);\n                            context.isClosePausedRef.current = false;\n                        }\n                    }\n                }[\"ToastViewport.useEffect.handleResume\"];\n                const handleFocusOutResume = {\n                    \"ToastViewport.useEffect.handleFocusOutResume\": (event)=>{\n                        const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                        if (isFocusMovingOutside) handleResume();\n                    }\n                }[\"ToastViewport.useEffect.handleFocusOutResume\"];\n                const handlePointerLeaveResume = {\n                    \"ToastViewport.useEffect.handlePointerLeaveResume\": ()=>{\n                        const isFocusInside = wrapper.contains(document.activeElement);\n                        if (!isFocusInside) handleResume();\n                    }\n                }[\"ToastViewport.useEffect.handlePointerLeaveResume\"];\n                wrapper.addEventListener(\"focusin\", handlePause);\n                wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.addEventListener(\"pointermove\", handlePause);\n                wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.addEventListener(\"blur\", handlePause);\n                window.addEventListener(\"focus\", handleResume);\n                return ({\n                    \"ToastViewport.useEffect\": ()=>{\n                        wrapper.removeEventListener(\"focusin\", handlePause);\n                        wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                        wrapper.removeEventListener(\"pointermove\", handlePause);\n                        wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                        window.removeEventListener(\"blur\", handlePause);\n                        window.removeEventListener(\"focus\", handleResume);\n                    }\n                })[\"ToastViewport.useEffect\"];\n            }\n        }\n    }[\"ToastViewport.useEffect\"], [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"ToastViewport.useCallback[getSortedTabbableCandidates]\": ({ tabbingDirection })=>{\n            const toastItems = getItems();\n            const tabbableCandidates = toastItems.map({\n                \"ToastViewport.useCallback[getSortedTabbableCandidates].tabbableCandidates\": (toastItem)=>{\n                    const toastNode = toastItem.ref.current;\n                    const toastTabbableCandidates = [\n                        toastNode,\n                        ...getTabbableCandidates(toastNode)\n                    ];\n                    return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n                }\n            }[\"ToastViewport.useCallback[getSortedTabbableCandidates].tabbableCandidates\"]);\n            return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n        }\n    }[\"ToastViewport.useCallback[getSortedTabbableCandidates]\"], [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastViewport.useEffect\": ()=>{\n            const viewport = ref.current;\n            if (viewport) {\n                const handleKeyDown = {\n                    \"ToastViewport.useEffect.handleKeyDown\": (event)=>{\n                        const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                        const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                        if (isTabKey) {\n                            const focusedElement = document.activeElement;\n                            const isTabbingBackwards = event.shiftKey;\n                            const targetIsViewport = event.target === viewport;\n                            if (targetIsViewport && isTabbingBackwards) {\n                                headFocusProxyRef.current?.focus();\n                                return;\n                            }\n                            const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                            const sortedCandidates = getSortedTabbableCandidates({\n                                tabbingDirection\n                            });\n                            const index = sortedCandidates.findIndex({\n                                \"ToastViewport.useEffect.handleKeyDown.index\": (candidate)=>candidate === focusedElement\n                            }[\"ToastViewport.useEffect.handleKeyDown.index\"]);\n                            if (focusFirst(sortedCandidates.slice(index + 1))) {\n                                event.preventDefault();\n                            } else {\n                                isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                            }\n                        }\n                    }\n                }[\"ToastViewport.useEffect.handleKeyDown\"];\n                viewport.addEventListener(\"keydown\", handleKeyDown);\n                return ({\n                    \"ToastViewport.useEffect\": ()=>viewport.removeEventListener(\"keydown\", handleKeyDown)\n                })[\"ToastViewport.useEffect\"];\n            }\n        }\n    }[\"ToastViewport.useEffect\"], [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? true,\n        onChange: onOpenChange,\n        caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, {\n        \"ToastImpl.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"ToastImpl.useComposedRefs[composedRefs]\"]);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)({\n        \"ToastImpl.useCallbackRef[handleClose]\": ()=>{\n            const isFocusInToast = node?.contains(document.activeElement);\n            if (isFocusInToast) context.viewport?.focus();\n            onClose();\n        }\n    }[\"ToastImpl.useCallbackRef[handleClose]\"]);\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"ToastImpl.useCallback[startTimer]\": (duration2)=>{\n            if (!duration2 || duration2 === Infinity) return;\n            window.clearTimeout(closeTimerRef.current);\n            closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n            closeTimerRef.current = window.setTimeout(handleClose, duration2);\n        }\n    }[\"ToastImpl.useCallback[startTimer]\"], [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastImpl.useEffect\": ()=>{\n            const viewport = context.viewport;\n            if (viewport) {\n                const handleResume = {\n                    \"ToastImpl.useEffect.handleResume\": ()=>{\n                        startTimer(closeTimerRemainingTimeRef.current);\n                        onResume?.();\n                    }\n                }[\"ToastImpl.useEffect.handleResume\"];\n                const handlePause = {\n                    \"ToastImpl.useEffect.handlePause\": ()=>{\n                        const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                        closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                        window.clearTimeout(closeTimerRef.current);\n                        onPause?.();\n                    }\n                }[\"ToastImpl.useEffect.handlePause\"];\n                viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n                return ({\n                    \"ToastImpl.useEffect\": ()=>{\n                        viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                        viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n                    }\n                })[\"ToastImpl.useEffect\"];\n            }\n        }\n    }[\"ToastImpl.useEffect\"], [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastImpl.useEffect\": ()=>{\n            if (open && !context.isClosePausedRef.current) startTimer(duration);\n        }\n    }[\"ToastImpl.useEffect\"], [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastImpl.useEffect\": ()=>{\n            onToastAdd();\n            return ({\n                \"ToastImpl.useEffect\": ()=>onToastRemove()\n            })[\"ToastImpl.useEffect\"];\n        }\n    }[\"ToastImpl.useEffect\"], [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ToastImpl.useMemo[announceTextContent]\": ()=>{\n            return node ? getAnnounceTextContent(node) : null;\n        }\n    }[\"ToastImpl.useMemo[announceTextContent]\"], [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                \"aria-atomic\": true,\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            role: \"status\",\n                            \"aria-live\": \"off\",\n                            \"aria-atomic\": true,\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame({\n        \"ToastAnnounce.useNextFrame\": ()=>setRenderAnnounceText(true)\n    }[\"ToastAnnounce.useNextFrame\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastAnnounce.useEffect\": ()=>{\n            const timer = window.setTimeout({\n                \"ToastAnnounce.useEffect.timer\": ()=>setIsAnnounced(true)\n            }[\"ToastAnnounce.useEffect.timer\"], 1e3);\n            return ({\n                \"ToastAnnounce.useEffect\": ()=>window.clearTimeout(timer)\n            })[\"ToastAnnounce.useEffect\"];\n        }\n    }[\"ToastAnnounce.useEffect\"], []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)({\n        \"useNextFrame.useLayoutEffect\": ()=>{\n            let raf1 = 0;\n            let raf2 = 0;\n            raf1 = window.requestAnimationFrame({\n                \"useNextFrame.useLayoutEffect\": ()=>raf2 = window.requestAnimationFrame(fn)\n            }[\"useNextFrame.useLayoutEffect\"]);\n            return ({\n                \"useNextFrame.useLayoutEffect\": ()=>{\n                    window.cancelAnimationFrame(raf1);\n                    window.cancelAnimationFrame(raf2);\n                }\n            })[\"useNextFrame.useLayoutEffect\"];\n        }\n    }[\"useNextFrame.useLayoutEffect\"], [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // src/tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const isOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipProvider.useEffect\": ()=>{\n            const skipDelayTimer = skipDelayTimerRef.current;\n            return ({\n                \"TooltipProvider.useEffect\": ()=>window.clearTimeout(skipDelayTimer)\n            })[\"TooltipProvider.useEffect\"];\n        }\n    }[\"TooltipProvider.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayedRef,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                isOpenDelayedRef.current = false;\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                skipDelayTimerRef.current = window.setTimeout({\n                    \"TooltipProvider.useCallback\": ()=>isOpenDelayedRef.current = true\n                }[\"TooltipProvider.useCallback\"], skipDelayDuration);\n            }\n        }[\"TooltipProvider.useCallback\"], [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": (inTransit)=>{\n                isPointerInTransitRef.current = inTransit;\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: {\n            \"Tooltip.useControllableState\": (open2)=>{\n                if (open2) {\n                    providerContext.onOpen();\n                    document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n                } else {\n                    providerContext.onClose();\n                }\n                onOpenChange?.(open2);\n            }\n        }[\"Tooltip.useControllableState\"],\n        caller: TOOLTIP_NAME\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Tooltip.useMemo[stateAttribute]\": ()=>{\n            return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n        }\n    }[\"Tooltip.useMemo[stateAttribute]\"], [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            wasOpenDelayedRef.current = false;\n            setOpen(true);\n        }\n    }[\"Tooltip.useCallback[handleOpen]\"], [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleClose]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            setOpen(false);\n        }\n    }[\"Tooltip.useCallback[handleClose]\"], [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = window.setTimeout({\n                \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n                    wasOpenDelayedRef.current = true;\n                    setOpen(true);\n                    openTimerRef.current = 0;\n                }\n            }[\"Tooltip.useCallback[handleDelayedOpen]\"], delayDuration);\n        }\n    }[\"Tooltip.useCallback[handleDelayedOpen]\"], [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Tooltip.useEffect\": ()=>{\n            return ({\n                \"Tooltip.useEffect\": ()=>{\n                    if (openTimerRef.current) {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            })[\"Tooltip.useEffect\"];\n        }\n    }[\"Tooltip.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n                    else handleOpen();\n                }\n            }[\"Tooltip.useCallback\"], [\n                providerContext.isOpenDelayedRef,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (disableHoverableContent) {\n                        handleClose();\n                    } else {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            }[\"Tooltip.useCallback\"], [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipTrigger.useCallback[handlePointerUp]\": ()=>isPointerDownRef.current = false\n    }[\"TooltipTrigger.useCallback[handlePointerUp]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipTrigger.useEffect\": ()=>{\n            return ({\n                \"TooltipTrigger.useEffect\": ()=>document.removeEventListener(\"pointerup\", handlePointerUp)\n            })[\"TooltipTrigger.useEffect\"];\n        }\n    }[\"TooltipTrigger.useEffect\"], [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                if (context.open) {\n                    context.onClose();\n                }\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\": ()=>{\n            setPointerGraceArea(null);\n            onPointerInTransitChange(false);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleCreateGraceArea]\": (event, hoverTarget)=>{\n            const currentTarget = event.currentTarget;\n            const exitPoint = {\n                x: event.clientX,\n                y: event.clientY\n            };\n            const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n            const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n            const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n            const graceArea = getHull([\n                ...paddedExitPoints,\n                ...hoverTargetPoints\n            ]);\n            setPointerGraceArea(graceArea);\n            onPointerInTransitChange(true);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleCreateGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            return ({\n                \"TooltipContentHoverable.useEffect\": ()=>handleRemoveGraceArea()\n            })[\"TooltipContentHoverable.useEffect\"];\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (trigger && content) {\n                const handleTriggerLeave = {\n                    \"TooltipContentHoverable.useEffect.handleTriggerLeave\": (event)=>handleCreateGraceArea(event, content)\n                }[\"TooltipContentHoverable.useEffect.handleTriggerLeave\"];\n                const handleContentLeave = {\n                    \"TooltipContentHoverable.useEffect.handleContentLeave\": (event)=>handleCreateGraceArea(event, trigger)\n                }[\"TooltipContentHoverable.useEffect.handleContentLeave\"];\n                trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n                content.addEventListener(\"pointerleave\", handleContentLeave);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>{\n                        trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                        content.removeEventListener(\"pointerleave\", handleContentLeave);\n                    }\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (pointerGraceArea) {\n                const handleTrackPointerGrace = {\n                    \"TooltipContentHoverable.useEffect.handleTrackPointerGrace\": (event)=>{\n                        const target = event.target;\n                        const pointerPosition = {\n                            x: event.clientX,\n                            y: event.clientY\n                        };\n                        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                        if (hasEnteredTarget) {\n                            handleRemoveGraceArea();\n                        } else if (isPointerOutsideGraceArea) {\n                            handleRemoveGraceArea();\n                            onClose();\n                        }\n                    }\n                }[\"TooltipContentHoverable.useEffect.handleTrackPointerGrace\"];\n                document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace)\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar Slottable = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.createSlottable)(\"TooltipContent\");\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            document.addEventListener(TOOLTIP_OPEN, onClose);\n            return ({\n                \"TooltipContentImpl.useEffect\": ()=>document.removeEventListener(TOOLTIP_OPEN, onClose)\n            })[\"TooltipContentImpl.useEffect\"];\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            if (context.trigger) {\n                const handleScroll = {\n                    \"TooltipContentImpl.useEffect.handleScroll\": (event)=>{\n                        const target = event.target;\n                        if (target?.contains(context.trigger)) onClose();\n                    }\n                }[\"TooltipContentImpl.useEffect.handleScroll\"];\n                window.addEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n                return ({\n                    \"TooltipContentImpl.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                            capture: true\n                        })\n                })[\"TooltipContentImpl.useEffect\"];\n            }\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvcnlhbndvbmcvZHlhZC1hcHBzL3ZpYmVjb2RlYmFzZS9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWNhbGxiYWNrLXJlZi9zcmMvdXNlLWNhbGxiYWNrLXJlZi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlQ2FsbGJhY2tSZWYoY2FsbGJhY2spIHtcbiAgY29uc3QgY2FsbGJhY2tSZWYgPSBSZWFjdC51c2VSZWYoY2FsbGJhY2spO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNhbGxiYWNrUmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgfSk7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICguLi5hcmdzKSA9PiBjYWxsYmFja1JlZi5jdXJyZW50Py4oLi4uYXJncyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUNhbGxiYWNrUmVmXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n      ref.current = callback;\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => ref.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lZmZlY3QtZXZlbnQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ29FO0FBQ3JDO0FBQy9CLDBCQUEwQix5TEFBSztBQUMvQiw4QkFBOEIseUxBQUs7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLHlDQUFZO0FBQzFCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJO0FBQ0osSUFBSSxrRkFBZTtBQUNuQjtBQUNBLEtBQUs7QUFDTDtBQUNBLFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvcnlhbndvbmcvZHlhZC1hcHBzL3ZpYmVjb2RlYmFzZS9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lZmZlY3QtZXZlbnQvZGlzdC9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3VzZS1lZmZlY3QtZXZlbnQudHN4XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0XCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VSZWFjdEVmZmVjdEV2ZW50ID0gUmVhY3RbXCIgdXNlRWZmZWN0RXZlbnQgXCIudHJpbSgpLnRvU3RyaW5nKCldO1xudmFyIHVzZVJlYWN0SW5zZXJ0aW9uRWZmZWN0ID0gUmVhY3RbXCIgdXNlSW5zZXJ0aW9uRWZmZWN0IFwiLnRyaW0oKS50b1N0cmluZygpXTtcbmZ1bmN0aW9uIHVzZUVmZmVjdEV2ZW50KGNhbGxiYWNrKSB7XG4gIGlmICh0eXBlb2YgdXNlUmVhY3RFZmZlY3RFdmVudCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmV0dXJuIHVzZVJlYWN0RWZmZWN0RXZlbnQoY2FsbGJhY2spO1xuICB9XG4gIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZigoKSA9PiB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQ2Fubm90IGNhbGwgYW4gZXZlbnQgaGFuZGxlciB3aGlsZSByZW5kZXJpbmcuXCIpO1xuICB9KTtcbiAgaWYgKHR5cGVvZiB1c2VSZWFjdEluc2VydGlvbkVmZmVjdCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdXNlUmVhY3RJbnNlcnRpb25FZmZlY3QoKCkgPT4ge1xuICAgICAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgICB9KTtcbiAgfSBlbHNlIHtcbiAgICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgICAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoLi4uYXJncykgPT4gcmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlRWZmZWN0RXZlbnRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvcnlhbndvbmcvZHlhZC1hcHBzL3ZpYmVjb2RlYmFzZS9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtZXNjYXBlLWtleWRvd24vc3JjL3VzZS1lc2NhcGUta2V5ZG93bi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWZcIjtcbmZ1bmN0aW9uIHVzZUVzY2FwZUtleWRvd24ob25Fc2NhcGVLZXlEb3duUHJvcCwgb3duZXJEb2N1bWVudCA9IGdsb2JhbFRoaXM/LmRvY3VtZW50KSB7XG4gIGNvbnN0IG9uRXNjYXBlS2V5RG93biA9IHVzZUNhbGxiYWNrUmVmKG9uRXNjYXBlS2V5RG93blByb3ApO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC5rZXkgPT09IFwiRXNjYXBlXCIpIHtcbiAgICAgICAgb25Fc2NhcGVLZXlEb3duKGV2ZW50KTtcbiAgICAgIH1cbiAgICB9O1xuICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93biwgeyBjYXB0dXJlOiB0cnVlIH0pO1xuICAgIHJldHVybiAoKSA9PiBvd25lckRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24sIHsgY2FwdHVyZTogdHJ1ZSB9KTtcbiAgfSwgW29uRXNjYXBlS2V5RG93biwgb3duZXJEb2N1bWVudF0pO1xufVxuZXhwb3J0IHtcbiAgdXNlRXNjYXBlS2V5ZG93blxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsOENBQThDLGtEQUFxQjtBQUNuRTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yeWFud29uZy9keWFkLWFwcHMvdmliZWNvZGViYXNlL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3QvZGlzdC9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZS1sYXlvdXQtZWZmZWN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlTGF5b3V0RWZmZWN0MiA9IGdsb2JhbFRoaXM/LmRvY3VtZW50ID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogKCkgPT4ge1xufTtcbmV4cG9ydCB7XG4gIHVzZUxheW91dEVmZmVjdDIgYXMgdXNlTGF5b3V0RWZmZWN0XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/use-size.tsx\n\n\nfunction useSize(element) {\n  const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUMrQjtBQUN1QjtBQUNkO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLHFCQUFxQiw2Q0FBZ0I7QUFDckM7QUFDQSwyQkFBMkIsc0RBQUc7QUFDOUIsTUFBTSxnRUFBUztBQUNmO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFLRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvcnlhbndvbmcvZHlhZC1hcHBzL3ZpYmVjb2RlYmFzZS9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlbi9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdmlzdWFsbHktaGlkZGVuLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZVwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgVklTVUFMTFlfSElEREVOX1NUWUxFUyA9IE9iamVjdC5mcmVlemUoe1xuICAvLyBTZWU6IGh0dHBzOi8vZ2l0aHViLmNvbS90d2JzL2Jvb3RzdHJhcC9ibG9iL21haW4vc2Nzcy9taXhpbnMvX3Zpc3VhbGx5LWhpZGRlbi5zY3NzXG4gIHBvc2l0aW9uOiBcImFic29sdXRlXCIsXG4gIGJvcmRlcjogMCxcbiAgd2lkdGg6IDEsXG4gIGhlaWdodDogMSxcbiAgcGFkZGluZzogMCxcbiAgbWFyZ2luOiAtMSxcbiAgb3ZlcmZsb3c6IFwiaGlkZGVuXCIsXG4gIGNsaXA6IFwicmVjdCgwLCAwLCAwLCAwKVwiLFxuICB3aGl0ZVNwYWNlOiBcIm5vd3JhcFwiLFxuICB3b3JkV3JhcDogXCJub3JtYWxcIlxufSk7XG52YXIgTkFNRSA9IFwiVmlzdWFsbHlIaWRkZW5cIjtcbnZhciBWaXN1YWxseUhpZGRlbiA9IFJlYWN0LmZvcndhcmRSZWYoXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICBQcmltaXRpdmUuc3BhbixcbiAgICAgIHtcbiAgICAgICAgLi4ucHJvcHMsXG4gICAgICAgIHJlZjogZm9yd2FyZGVkUmVmLFxuICAgICAgICBzdHlsZTogeyAuLi5WSVNVQUxMWV9ISURERU5fU1RZTEVTLCAuLi5wcm9wcy5zdHlsZSB9XG4gICAgICB9XG4gICAgKTtcbiAgfVxuKTtcblZpc3VhbGx5SGlkZGVuLmRpc3BsYXlOYW1lID0gTkFNRTtcbnZhciBSb290ID0gVmlzdWFsbHlIaWRkZW47XG5leHBvcnQge1xuICBSb290LFxuICBWSVNVQUxMWV9ISURERU5fU1RZTEVTLFxuICBWaXN1YWxseUhpZGRlblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;