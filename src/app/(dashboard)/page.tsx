import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Settings,
  Table,
  Users,
  Code,
  TerminalSquare,
  Database,
  ArrowRight,
  CheckCircle,
  Clock,
  AlertCircle,
} from "lucide-react";

const quickActions = [
  {
    title: "Configure Database",
    description: "Set up your database connection",
    href: "/configuration",
    icon: Settings,
    color: "bg-blue-500",
  },
  {
    title: "Browse Tables",
    description: "Explore your database tables",
    href: "/tables",
    icon: Table,
    color: "bg-green-500",
  },
  {
    title: "SQL Editor",
    description: "Run custom queries",
    href: "/sql-editor",
    icon: Code,
    color: "bg-purple-500",
  },
  {
    title: "Manage Teams",
    description: "Handle team permissions",
    href: "/teams",
    icon: Users,
    color: "bg-orange-500",
  },
];

const recentActivity = [
  {
    action: "Database connected",
    time: "2 minutes ago",
    status: "success",
    icon: CheckCircle,
  },
  {
    action: "Query executed",
    time: "5 minutes ago",
    status: "success",
    icon: CheckCircle,
  },
  {
    action: "Table schema updated",
    time: "1 hour ago",
    status: "pending",
    icon: Clock,
  },
  {
    action: "Connection timeout",
    time: "2 hours ago",
    status: "error",
    icon: AlertCircle,
  },
];

export default function DashboardPage() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Welcome to VibeCodeBase - your database management hub
        </p>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Link key={action.title} href={action.href}>
                <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{action.title}</h3>
                      <p className="text-sm text-gray-600">{action.description}</p>
                    </div>
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Getting Started */}
      <Card>
        <CardHeader>
          <CardTitle>Getting Started</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-gray-700">
          <p>
            1. Navigate to the{" "}
            <Link
              href="/configuration"
              className="text-blue-600 hover:underline font-medium"
            >
              Configuration
            </Link>{" "}
            page to set up your database connection details.
          </p>
          <p>
            2. Once configured, you can explore your{" "}
            <Link
              href="/tables"
              className="text-blue-600 hover:underline font-medium"
            >
              Tables
            </Link>
            , manage{" "}
            <Link
              href="/teams"
              className="text-blue-600 hover:underline font-medium"
            >
              Teams
            </Link>
            , or run queries in the{" "}
            <Link
              href="/sql-editor"
              className="text-blue-600 hover:underline font-medium"
            >
              SQL Editor
            </Link>
            .
          </p>
          <p>
            3. Use{" "}
            <Link
              href="/edge-functions"
              className="text-blue-600 hover:underline font-medium"
            >
              Edge Functions
            </Link>{" "}
            to deploy serverless functions for your database operations.
          </p>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div
                  className={`p-1 rounded-full ${
                    activity.status === "success"
                      ? "bg-green-100 text-green-600"
                      : activity.status === "pending"
                      ? "bg-yellow-100 text-yellow-600"
                      : "bg-red-100 text-red-600"
                  }`}
                >
                  <activity.icon className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.action}
                  </p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
                <Badge
                  variant={
                    activity.status === "success"
                      ? "default"
                      : activity.status === "pending"
                      ? "secondary"
                      : "destructive"
                  }
                >
                  {activity.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
