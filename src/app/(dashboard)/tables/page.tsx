import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { HardDriveDownload } from "lucide-react";

const mockTables = [
  { name: "users", columns: 5, rows: 1204, size: "1.2MB" },
  { name: "products", columns: 8, rows: 560, size: "800KB" },
  { name: "orders", columns: 12, rows: 10250, size: "5.5MB" },
  { name: "categories", columns: 3, rows: 15, size: "50KB" },
];

export default function TablesPage() {
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-semibold text-gray-800">Database Tables</h1>
      <Card>
        <CardHeader>
          <CardTitle>Tables Overview</CardTitle>
          <CardDescription>List of tables in your connected database. Click on a table to view its structure and data.</CardDescription>
        </CardHeader>
        <CardContent>
          {mockTables.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead className="text-right">Columns</TableHead>
                  <TableHead className="text-right">Rows (Est.)</TableHead>
                  <TableHead className="text-right">Size (Est.)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockTables.map((table) => (
                  <TableRow key={table.name} className="hover:bg-gray-50 cursor-pointer">
                    <TableCell className="font-medium">{table.name}</TableCell>
                    <TableCell className="text-right">{table.columns}</TableCell>
                    <TableCell className="text-right">{table.rows.toLocaleString()}</TableCell>
                    <TableCell className="text-right">{table.size}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <HardDriveDownload className="h-16 w-16 text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-600">No tables found or database not connected.</p>
              <p className="text-sm text-gray-500">Please check your configuration or ensure your database has tables.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
