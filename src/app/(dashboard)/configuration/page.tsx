"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Save } from "lucide-react";

export default function ConfigurationPage() {
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-semibold text-gray-800">Configuration</h1>
      <Card className="w-full max-w-lg">
        <CardHeader>
          <CardTitle>PlanetScale Connection</CardTitle>
          <CardDescription>Enter your PlanetScale database credentials. These will be used to connect to your database. Be sure to keep them secure.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="host">Host</Label>
            <Input id="host" placeholder="aws.connect.psdb.cloud" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input id="username" placeholder="your-username" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input id="password" type="password" placeholder="your-password" />
          </div>
        </CardContent>
        <CardFooter>
          <Button className="ml-auto">
            <Save className="mr-2 h-4 w-4" />
            Save Configuration
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
